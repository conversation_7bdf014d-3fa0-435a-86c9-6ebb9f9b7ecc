"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"

interface StaffMember {
  id: string
  name: string
  role: string
  locations: string[]
}

interface LocationCounts {
  [key: string]: number
}

export default function TestStaffAssignmentsPage() {
  const [staff, setStaff] = useState<StaffMember[]>([])
  const [locationCounts, setLocationCounts] = useState<LocationCounts>({})
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchStaffData()
  }, [])

  const fetchStaffData = async () => {
    try {
      const response = await fetch('/api/staff')
      const data = await response.json()
      
      if (data.staff) {
        setStaff(data.staff)
        
        // Calculate location counts
        const counts: LocationCounts = {
          'loc1': 0,
          'loc2': 0,
          'loc3': 0,
          'home': 0,
          'none': 0
        }
        
        data.staff.forEach((member: StaffMember) => {
          if (member.locations && member.locations.length > 0) {
            member.locations.forEach(locationId => {
              if (counts[locationId] !== undefined) {
                counts[locationId]++
              }
            })
          } else {
            counts['none']++
          }
        })
        
        setLocationCounts(counts)
      }
    } catch (error) {
      console.error('Error fetching staff data:', error)
    } finally {
      setLoading(false)
    }
  }

  const getLocationName = (locationId: string) => {
    switch (locationId) {
      case 'loc1': return 'D-ring road'
      case 'loc2': return 'Muaither'
      case 'loc3': return 'Medinat Khalifa'
      case 'home': return 'Home Service'
      default: return locationId
    }
  }

  const getStaffByLocation = (locationId: string) => {
    return staff.filter(member => 
      member.locations && member.locations.includes(locationId)
    )
  }

  const getStaffWithoutLocation = () => {
    return staff.filter(member => 
      !member.locations || member.locations.length === 0
    )
  }

  if (loading) {
    return (
      <div className="container mx-auto p-6">
        <div className="text-center">Loading staff assignments...</div>
      </div>
    )
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div>
        <h1 className="text-3xl font-bold">Test Staff Location Assignments</h1>
        <p className="text-muted-foreground">
          Verify that staff members are properly assigned to their locations
        </p>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader>
            <CardTitle>D-ring road (loc1)</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{locationCounts['loc1']} staff</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle>Muaither (loc2)</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{locationCounts['loc2']} staff</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle>Medinat Khalifa (loc3)</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-600">{locationCounts['loc3']} staff</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle>No Location</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{locationCounts['none']} staff</div>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Staff Lists */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {/* D-ring road */}
        <Card>
          <CardHeader>
            <CardTitle>D-ring road (loc1)</CardTitle>
            <CardDescription>{locationCounts['loc1']} staff members</CardDescription>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2">
              {getStaffByLocation('loc1').map(member => (
                <li key={member.id} className="flex justify-between">
                  <span>{member.name}</span>
                  <span className="text-sm text-muted-foreground">{member.role}</span>
                </li>
              ))}
            </ul>
          </CardContent>
        </Card>

        {/* Muaither */}
        <Card>
          <CardHeader>
            <CardTitle>Muaither (loc2)</CardTitle>
            <CardDescription>{locationCounts['loc2']} staff members</CardDescription>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2">
              {getStaffByLocation('loc2').map(member => (
                <li key={member.id} className="flex justify-between">
                  <span>{member.name}</span>
                  <span className="text-sm text-muted-foreground">{member.role}</span>
                </li>
              ))}
            </ul>
          </CardContent>
        </Card>

        {/* Medinat Khalifa */}
        <Card>
          <CardHeader>
            <CardTitle>Medinat Khalifa (loc3)</CardTitle>
            <CardDescription>{locationCounts['loc3']} staff members</CardDescription>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2">
              {getStaffByLocation('loc3').map(member => (
                <li key={member.id} className="flex justify-between">
                  <span>{member.name}</span>
                  <span className="text-sm text-muted-foreground">{member.role}</span>
                </li>
              ))}
            </ul>
          </CardContent>
        </Card>
      </div>

      {/* Staff without locations */}
      {locationCounts['none'] > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-red-600">Staff Without Location Assignments</CardTitle>
            <CardDescription>These staff members need to be assigned to locations</CardDescription>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2">
              {getStaffWithoutLocation().map(member => (
                <li key={member.id} className="flex justify-between">
                  <span>{member.name}</span>
                  <span className="text-sm text-muted-foreground">{member.role}</span>
                </li>
              ))}
            </ul>
          </CardContent>
        </Card>
      )}

      {/* Actions */}
      <div className="space-y-4">
        <h2 className="text-xl font-semibold">Next Steps</h2>
        <div className="bg-blue-50 p-4 rounded-lg">
          <ol className="list-decimal list-inside space-y-2 text-sm">
            <li><strong>Test Calendar View</strong> - Go to the calendar and verify staff appear in correct locations</li>
            <li><strong>Test Location Filtering</strong> - Switch between locations and verify filtering works</li>
            <li><strong>Test Appointments</strong> - Create appointments and verify staff availability by location</li>
            <li><strong>Test Access Control</strong> - Verify location-based access restrictions work properly</li>
          </ol>
        </div>
      </div>
    </div>
  )
}
