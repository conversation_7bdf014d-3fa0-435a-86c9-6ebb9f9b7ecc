"use client"

import { useState, useEffect } from "react"
import { useAuth } from "@/lib/auth-provider"
import { useLocations } from "@/lib/location-provider"
import { useStaff } from "@/lib/staff-provider"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"

export default function CalendarLocationDebugPage() {
  const { user, currentLocation, setCurrentLocation, canAccessLocation, getUserAccessibleLocations } = useAuth()
  const { locations, getActiveLocations, refreshLocations } = useLocations()
  const { staff, getStaffByLocation } = useStaff()
  const [debugInfo, setDebugInfo] = useState<any>({})

  useEffect(() => {
    const gatherDebugInfo = async () => {
      try {
        // Fetch API data directly
        const locationsResponse = await fetch('/api/locations')
        const locationsData = await locationsResponse.json()
        
        const staffResponse = await fetch('/api/staff')
        const staffData = await staffResponse.json()

        // Get provider data
        const activeLocations = getActiveLocations()
        const dRingStaff = getStaffByLocation('loc1')
        const allStaff = getStaffByLocation('all')

        setDebugInfo({
          user,
          currentLocation,
          userAccessibleLocations: getUserAccessibleLocations(),
          
          // API Data
          apiLocations: locationsData.locations || [],
          apiStaff: staffData.staff || [],
          
          // Provider Data
          providerLocations: locations,
          activeLocations,
          providerStaff: staff,
          
          // Location-specific data
          dRingLocation: locationsData.locations?.find((loc: any) => loc.id === 'loc1'),
          dRingStaff,
          allStaff,
          
          // Access checks
          canAccessDRing: canAccessLocation('loc1'),
          canAccessAll: canAccessLocation('all'),
        })
      } catch (error) {
        console.error('Error gathering debug info:', error)
      }
    }

    gatherDebugInfo()
  }, [user, currentLocation, locations, staff, canAccessLocation, getUserAccessibleLocations, getActiveLocations, getStaffByLocation])

  const refreshData = async () => {
    await refreshLocations()
    window.location.reload()
  }

  const switchToLocation = (locationId: string) => {
    setCurrentLocation(locationId)
  }

  return (
    <div className="p-6 space-y-6">
      <h1 className="text-2xl font-bold">Calendar Location & Staff Debug</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Current State */}
        <Card>
          <CardHeader>
            <CardTitle>Current State</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <p><strong>User:</strong> {debugInfo.user?.name} ({debugInfo.user?.role})</p>
            <p><strong>Current Location:</strong> {debugInfo.currentLocation}</p>
            <p><strong>User Locations:</strong> {debugInfo.user?.locations?.join(', ')}</p>
            <p><strong>Accessible Locations:</strong> {debugInfo.userAccessibleLocations?.join(', ')}</p>
            <p><strong>Can Access D-Ring:</strong> {debugInfo.canAccessDRing ? 'Yes' : 'No'}</p>
            <p><strong>Can Access All:</strong> {debugInfo.canAccessAll ? 'Yes' : 'No'}</p>
          </CardContent>
        </Card>

        {/* API Locations */}
        <Card>
          <CardHeader>
            <CardTitle>API Locations</CardTitle>
            <CardDescription>Direct from /api/locations</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {debugInfo.apiLocations?.map((loc: any) => (
                <div key={loc.id} className="p-2 border rounded">
                  <p><strong>{loc.name}</strong> ({loc.id})</p>
                  <p>Active: {loc.isActive ? 'Yes' : 'No'}</p>
                </div>
              )) || <p>No API locations found</p>}
            </div>
          </CardContent>
        </Card>

        {/* Provider Locations */}
        <Card>
          <CardHeader>
            <CardTitle>Provider Locations</CardTitle>
            <CardDescription>From useLocations hook</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {debugInfo.providerLocations?.map((loc: any) => (
                <div key={loc.id} className="p-2 border rounded">
                  <p><strong>{loc.name}</strong> ({loc.id})</p>
                  <p>Status: {loc.status}</p>
                </div>
              )) || <p>No provider locations found</p>}
            </div>
          </CardContent>
        </Card>

        {/* Active Locations */}
        <Card>
          <CardHeader>
            <CardTitle>Active Locations</CardTitle>
            <CardDescription>From getActiveLocations()</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {debugInfo.activeLocations?.map((loc: any) => (
                <div key={loc.id} className="p-2 border rounded">
                  <p><strong>{loc.name}</strong> ({loc.id})</p>
                  <p>Status: {loc.status}</p>
                </div>
              )) || <p>No active locations found</p>}
            </div>
          </CardContent>
        </Card>

        {/* D-Ring Location Details */}
        <Card>
          <CardHeader>
            <CardTitle>D-Ring Road Location</CardTitle>
          </CardHeader>
          <CardContent>
            {debugInfo.dRingLocation ? (
              <div className="space-y-2">
                <p><strong>ID:</strong> {debugInfo.dRingLocation.id}</p>
                <p><strong>Name:</strong> {debugInfo.dRingLocation.name}</p>
                <p><strong>Active:</strong> {debugInfo.dRingLocation.isActive ? 'Yes' : 'No'}</p>
                <p><strong>Address:</strong> {debugInfo.dRingLocation.address}</p>
              </div>
            ) : (
              <p className="text-red-600">❌ D-Ring Road location not found in API</p>
            )}
          </CardContent>
        </Card>

        {/* D-Ring Staff */}
        <Card>
          <CardHeader>
            <CardTitle>D-Ring Road Staff</CardTitle>
            <CardDescription>From getStaffByLocation('loc1')</CardDescription>
          </CardHeader>
          <CardContent>
            {debugInfo.dRingStaff?.length > 0 ? (
              <div className="space-y-2">
                <p><strong>Count:</strong> {debugInfo.dRingStaff.length}</p>
                {debugInfo.dRingStaff.map((staff: any) => (
                  <div key={staff.id} className="p-2 border rounded">
                    <p><strong>{staff.name}</strong></p>
                    <p>Role: {staff.role}</p>
                    <p>Locations: {staff.locations?.join(', ')}</p>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-red-600">❌ No staff found for D-Ring Road</p>
            )}
          </CardContent>
        </Card>

        {/* All Staff Summary */}
        <Card>
          <CardHeader>
            <CardTitle>All Staff Summary</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <p><strong>Total Staff (API):</strong> {debugInfo.apiStaff?.length || 0}</p>
              <p><strong>Total Staff (Provider):</strong> {debugInfo.providerStaff?.length || 0}</p>
              <p><strong>All Staff (Provider):</strong> {debugInfo.allStaff?.length || 0}</p>
              
              <div className="mt-4">
                <h4 className="font-medium">Staff by Location:</h4>
                <ul className="text-sm space-y-1">
                  <li>D-Ring (loc1): {debugInfo.dRingStaff?.length || 0}</li>
                  <li>Muaither (loc2): {getStaffByLocation('loc2')?.length || 0}</li>
                  <li>Medinat Khalifa (loc3): {getStaffByLocation('loc3')?.length || 0}</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* API Staff Details */}
        <Card>
          <CardHeader>
            <CardTitle>API Staff Details</CardTitle>
            <CardDescription>Staff with location assignments</CardDescription>
          </CardHeader>
          <CardContent className="max-h-60 overflow-y-auto">
            {debugInfo.apiStaff?.filter((staff: any) => staff.locations?.includes('loc1')).map((staff: any) => (
              <div key={staff.id} className="p-2 border rounded mb-2">
                <p><strong>{staff.name}</strong></p>
                <p>Email: {staff.email}</p>
                <p>Locations: {staff.locations?.join(', ')}</p>
                <p>Status: {staff.status}</p>
              </div>
            )) || <p>No staff assigned to D-Ring Road in API</p>}
          </CardContent>
        </Card>
      </div>

      {/* Actions */}
      <div className="space-y-4">
        <h2 className="text-xl font-semibold">Test Actions</h2>
        <div className="flex gap-4">
          <Button onClick={refreshData}>
            Refresh All Data
          </Button>
          <Button onClick={() => switchToLocation('loc1')} variant="outline">
            Switch to D-Ring Road
          </Button>
          <Button onClick={() => switchToLocation('all')} variant="outline">
            Switch to All Locations
          </Button>
          <Button onClick={() => window.location.href = '/dashboard/appointments'} variant="outline">
            Go to Appointments
          </Button>
        </div>
      </div>

      {/* Issue Analysis */}
      <Card>
        <CardHeader>
          <CardTitle>Issue Analysis</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <h4 className="font-medium">Possible Issues:</h4>
              <ul className="text-sm space-y-1 text-gray-600">
                <li>• D-Ring Road location not in provider locations</li>
                <li>• Staff not properly assigned to D-Ring Road location</li>
                <li>• Location provider not loading data from API</li>
                <li>• Staff provider not filtering correctly</li>
                <li>• Calendar component not using correct data source</li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-medium">Expected Behavior:</h4>
              <ul className="text-sm space-y-1 text-gray-600">
                <li>• D-Ring Road should appear in location selector</li>
                <li>• Mekdes Abebe should appear in staff list for D-Ring Road</li>
                <li>• Calendar should show appointments for D-Ring Road</li>
                <li>• Location switching should work properly</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
