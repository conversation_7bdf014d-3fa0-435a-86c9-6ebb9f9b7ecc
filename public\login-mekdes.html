<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login as Mekdes</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .button {
            background-color: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        .button:hover {
            background-color: #0056b3;
        }
        .success {
            color: #28a745;
            background-color: #d4edda;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .error {
            color: #dc3545;
            background-color: #f8d7da;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .info {
            color: #0c5460;
            background-color: #d1ecf1;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .code {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            font-family: monospace;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Login as Mekdes Abebe</h1>
        <p>This will log you in as Mekdes Abebe, a staff member with access only to D-Ring Road location.</p>
        
        <button class="button" onclick="loginAsMekdes()">Login as Mekdes</button>
        <button class="button" onclick="checkCurrentUser()">Check Current User</button>
        <button class="button" onclick="logout()">Logout</button>
        
        <div id="results"></div>
        
        <h3>Alternative: Browser Console Method</h3>
        <p>If the buttons above don't work, you can copy and paste this code into your browser console (F12 → Console):</p>
        <div class="code">
// Login as Mekdes Abebe<br>
const mekdesUser = {<br>
&nbsp;&nbsp;id: "staff-real-1",<br>
&nbsp;&nbsp;name: "Mekdes Abebe",<br>
&nbsp;&nbsp;email: "<EMAIL>",<br>
&nbsp;&nbsp;role: "staff",<br>
&nbsp;&nbsp;locations: ["loc1"]<br>
};<br>
localStorage.setItem('vanity_user', JSON.stringify(mekdesUser));<br>
localStorage.setItem('vanity_location', 'loc1');<br>
console.log('✅ Logged in as Mekdes Abebe');<br>
window.location.href = '/dashboard/appointments';
        </div>
    </div>

    <script>
        function loginAsMekdes() {
            const results = document.getElementById('results');
            
            try {
                // Set up Mekdes as the logged-in user
                const mekdesUser = {
                    id: "staff-real-1",
                    name: "Mekdes Abebe",
                    email: "<EMAIL>",
                    role: "staff",
                    locations: ["loc1"] // Only D-Ring Road
                };
                
                // Store user data
                localStorage.setItem('vanity_user', JSON.stringify(mekdesUser));
                localStorage.setItem('vanity_location', 'loc1');
                
                results.innerHTML = `
                    <div class="success">
                        <h4>✅ Successfully logged in as Mekdes Abebe!</h4>
                        <ul>
                            <li><strong>Name:</strong> ${mekdesUser.name}</li>
                            <li><strong>Email:</strong> ${mekdesUser.email}</li>
                            <li><strong>Role:</strong> ${mekdesUser.role}</li>
                            <li><strong>Location Access:</strong> D-Ring Road only</li>
                        </ul>
                        <p><strong>Next:</strong> Go to the <a href="/dashboard/appointments" target="_blank">Appointments page</a> to see D-Ring Road data!</p>
                    </div>
                `;
                
                // Auto-redirect after 2 seconds
                setTimeout(() => {
                    window.location.href = '/dashboard/appointments';
                }, 2000);
            } catch (error) {
                results.innerHTML = `<div class="error">Error: ${error.message}</div>`;
            }
        }

        function checkCurrentUser() {
            const results = document.getElementById('results');
            
            try {
                const user = localStorage.getItem('vanity_user');
                const location = localStorage.getItem('vanity_location');
                
                if (user) {
                    const userData = JSON.parse(user);
                    results.innerHTML = `
                        <div class="info">
                            <h4>Current User:</h4>
                            <ul>
                                <li><strong>Name:</strong> ${userData.name}</li>
                                <li><strong>Email:</strong> ${userData.email}</li>
                                <li><strong>Role:</strong> ${userData.role}</li>
                                <li><strong>Locations:</strong> ${userData.locations.join(', ')}</li>
                                <li><strong>Current Location:</strong> ${location}</li>
                            </ul>
                        </div>
                    `;
                } else {
                    results.innerHTML = '<div class="error">No user is currently logged in.</div>';
                }
            } catch (error) {
                results.innerHTML = `<div class="error">Error: ${error.message}</div>`;
            }
        }

        function logout() {
            const results = document.getElementById('results');
            
            try {
                localStorage.removeItem('vanity_user');
                localStorage.removeItem('vanity_location');
                
                results.innerHTML = '<div class="success">✅ Successfully logged out!</div>';
            } catch (error) {
                results.innerHTML = `<div class="error">Error: ${error.message}</div>`;
            }
        }

        // Auto-check current user on page load
        window.onload = function() {
            checkCurrentUser();
        };
    </script>
</body>
</html>
