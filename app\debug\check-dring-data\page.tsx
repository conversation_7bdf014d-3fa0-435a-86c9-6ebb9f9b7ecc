"use client"

import { useState, useEffect } from "react"
import { useAuth } from "@/lib/auth-provider"
import { useLocations } from "@/lib/location-provider"
import { useStaff } from "@/lib/staff-provider"

export default function CheckDRingDataPage() {
  const [apiData, setApiData] = useState<any>({})
  const [isLoading, setIsLoading] = useState(true)
  const { user, currentLocation, canAccessLocation, getUserAccessibleLocations } = useAuth()
  const { locations, getActiveLocations } = useLocations()
  const { staff, getStaffByLocation } = useStaff()

  useEffect(() => {
    const fetchData = async () => {
      try {
        // Fetch locations from API
        const locationsResponse = await fetch('/api/locations')
        const locationsData = await locationsResponse.json()

        // Fetch staff from API
        const staffResponse = await fetch('/api/staff')
        const staffData = await staffResponse.json()

        // Fetch appointments from localStorage
        const appointments = JSON.parse(localStorage.getItem('vanity_appointments') || '[]')

        setApiData({
          locations: locationsData,
          staff: staffData,
          appointments: appointments,
          dRingLocation: locationsData.locations?.find((loc: any) => 
            loc.name.toLowerCase().includes('d-ring') || loc.id === 'loc1'
          ),
          dRingStaff: staffData.staff?.filter((s: any) => 
            s.locations?.includes('loc1')
          ),
          dRingAppointments: appointments.filter((apt: any) => apt.location === 'loc1')
        })
      } catch (error) {
        console.error('Error fetching data:', error)
      } finally {
        setIsLoading(false)
      }
    }

    fetchData()
  }, [])

  const dRingStaffFromProvider = getStaffByLocation('loc1')

  if (isLoading) {
    return <div className="p-6">Loading...</div>
  }

  return (
    <div className="p-6 space-y-6">
      <h1 className="text-2xl font-bold">D-Ring Road Data Check</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Current User Info */}
        <div className="space-y-4">
          <h2 className="text-xl font-semibold">Current User</h2>
          <div className="bg-gray-100 p-4 rounded">
            <p><strong>Name:</strong> {user?.name}</p>
            <p><strong>Role:</strong> {user?.role}</p>
            <p><strong>Locations:</strong> {user?.locations?.join(', ')}</p>
            <p><strong>Current Location:</strong> {currentLocation}</p>
            <p><strong>Can Access D-Ring (loc1):</strong> {canAccessLocation('loc1') ? 'Yes' : 'No'}</p>
            <p><strong>Accessible Locations:</strong> {getUserAccessibleLocations()?.join(', ')}</p>
          </div>
        </div>

        {/* D-Ring Location Info */}
        <div className="space-y-4">
          <h2 className="text-xl font-semibold">D-Ring Road Location</h2>
          <div className="bg-gray-100 p-4 rounded">
            {apiData.dRingLocation ? (
              <div>
                <p><strong>ID:</strong> {apiData.dRingLocation.id}</p>
                <p><strong>Name:</strong> {apiData.dRingLocation.name}</p>
                <p><strong>Active:</strong> {apiData.dRingLocation.isActive ? 'Yes' : 'No'}</p>
                <p><strong>Address:</strong> {apiData.dRingLocation.address}</p>
              </div>
            ) : (
              <p className="text-red-600">❌ D-Ring Road location not found in database</p>
            )}
          </div>
        </div>

        {/* All Locations */}
        <div className="space-y-4">
          <h2 className="text-xl font-semibold">All Locations (API)</h2>
          <div className="bg-gray-100 p-4 rounded max-h-60 overflow-y-auto">
            {apiData.locations?.locations?.map((loc: any) => (
              <div key={loc.id} className="mb-2 p-2 border-b">
                <p><strong>{loc.name}</strong> ({loc.id})</p>
                <p>Active: {loc.isActive ? 'Yes' : 'No'}</p>
              </div>
            )) || <p>No locations found</p>}
          </div>
        </div>

        {/* All Locations (Provider) */}
        <div className="space-y-4">
          <h2 className="text-xl font-semibold">All Locations (Provider)</h2>
          <div className="bg-gray-100 p-4 rounded max-h-60 overflow-y-auto">
            {locations?.map((loc: any) => (
              <div key={loc.id} className="mb-2 p-2 border-b">
                <p><strong>{loc.name}</strong> ({loc.id})</p>
                <p>Status: {loc.status}</p>
              </div>
            )) || <p>No locations found</p>}
          </div>
        </div>

        {/* D-Ring Staff (API) */}
        <div className="space-y-4">
          <h2 className="text-xl font-semibold">D-Ring Staff (API)</h2>
          <div className="bg-gray-100 p-4 rounded">
            {apiData.dRingStaff?.length > 0 ? (
              apiData.dRingStaff.map((staff: any) => (
                <div key={staff.id} className="mb-2 p-2 border-b">
                  <p><strong>{staff.name}</strong> ({staff.id})</p>
                  <p>Email: {staff.email}</p>
                  <p>Locations: {staff.locations?.join(', ')}</p>
                  <p>Status: {staff.status}</p>
                </div>
              ))
            ) : (
              <p className="text-red-600">❌ No staff assigned to D-Ring Road</p>
            )}
          </div>
        </div>

        {/* D-Ring Staff (Provider) */}
        <div className="space-y-4">
          <h2 className="text-xl font-semibold">D-Ring Staff (Provider)</h2>
          <div className="bg-gray-100 p-4 rounded">
            {dRingStaffFromProvider?.length > 0 ? (
              dRingStaffFromProvider.map((staff: any) => (
                <div key={staff.id} className="mb-2 p-2 border-b">
                  <p><strong>{staff.name}</strong> ({staff.id})</p>
                  <p>Email: {staff.email}</p>
                  <p>Locations: {staff.locations?.join(', ')}</p>
                  <p>Status: {staff.status}</p>
                </div>
              ))
            ) : (
              <p className="text-red-600">❌ No staff found via provider for D-Ring Road</p>
            )}
          </div>
        </div>

        {/* All Staff Summary */}
        <div className="space-y-4">
          <h2 className="text-xl font-semibold">All Staff Summary</h2>
          <div className="bg-gray-100 p-4 rounded">
            <p><strong>Total Staff (API):</strong> {apiData.staff?.staff?.length || 0}</p>
            <p><strong>Total Staff (Provider):</strong> {staff?.length || 0}</p>
            <div className="mt-2">
              <p><strong>Staff by Location:</strong></p>
              <ul className="ml-4">
                <li>All: {getStaffByLocation('all')?.length || 0}</li>
                <li>D-Ring (loc1): {getStaffByLocation('loc1')?.length || 0}</li>
                <li>Muaither (loc2): {getStaffByLocation('loc2')?.length || 0}</li>
                <li>Home: {getStaffByLocation('home')?.length || 0}</li>
              </ul>
            </div>
          </div>
        </div>

        {/* D-Ring Appointments */}
        <div className="space-y-4">
          <h2 className="text-xl font-semibold">D-Ring Appointments</h2>
          <div className="bg-gray-100 p-4 rounded">
            {apiData.dRingAppointments?.length > 0 ? (
              <div>
                <p><strong>Count:</strong> {apiData.dRingAppointments.length}</p>
                {apiData.dRingAppointments.slice(0, 3).map((apt: any) => (
                  <div key={apt.id} className="mb-2 p-2 border-b">
                    <p><strong>{apt.clientName}</strong></p>
                    <p>Service: {apt.service}</p>
                    <p>Staff: {apt.staffName}</p>
                    <p>Date: {apt.date}</p>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-orange-600">⚠️ No appointments for D-Ring Road</p>
            )}
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="space-y-4">
        <h2 className="text-xl font-semibold">Quick Actions</h2>
        <div className="flex gap-4">
          <button
            onClick={() => window.location.href = '/debug/fix-dring-access'}
            className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
          >
            Fix D-Ring Access
          </button>
          <button
            onClick={() => window.location.href = '/dashboard/appointments'}
            className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
          >
            Go to Appointments
          </button>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700"
          >
            Refresh Page
          </button>
        </div>
      </div>
    </div>
  )
}
