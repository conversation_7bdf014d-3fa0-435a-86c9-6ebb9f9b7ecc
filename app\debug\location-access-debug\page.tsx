"use client"

import { useAuth } from "@/lib/auth-provider"
import { useLocations } from "@/lib/location-provider"
import { useStaff } from "@/lib/staff-provider"
import { useEffect, useState } from "react"

export default function LocationAccessDebugPage() {
  const { user, currentLocation, canAccessLocation, getUserAccessibleLocations } = useAuth()
  const { locations, getActiveLocations } = useLocations()
  const { staff } = useStaff()
  const [debugInfo, setDebugInfo] = useState<any>({})

  useEffect(() => {
    // Gather debug information
    const info = {
      user: user,
      currentLocation: currentLocation,
      userAccessibleLocations: getUserAccessibleLocations(),
      allLocations: locations,
      activeLocations: getActiveLocations(),
      allStaff: staff,
      dRingStaff: staff.filter(s => s.locations.includes('loc1')),
      canAccessDRing: canAccessLocation('loc1'),
      canAccessMuaither: canAccessLocation('loc2'),
      canAccessAll: canAccessLocation('all'),
      localStorage: {
        user: localStorage.getItem('vanity_user'),
        location: localStorage.getItem('vanity_location'),
        staff: localStorage.getItem('vanity_staff'),
        locations: localStorage.getItem('vanity_locations')
      }
    }
    setDebugInfo(info)
  }, [user, currentLocation, locations, staff, canAccessLocation, getUserAccessibleLocations, getActiveLocations])

  return (
    <div className="p-6 space-y-6">
      <h1 className="text-2xl font-bold">Location Access Debug</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-4">
          <h2 className="text-xl font-semibold">Current User</h2>
          <div className="bg-gray-100 p-4 rounded">
            <pre className="text-sm overflow-auto">
              {JSON.stringify(debugInfo.user, null, 2)}
            </pre>
          </div>
        </div>

        <div className="space-y-4">
          <h2 className="text-xl font-semibold">Location Access</h2>
          <div className="bg-gray-100 p-4 rounded">
            <p><strong>Current Location:</strong> {debugInfo.currentLocation}</p>
            <p><strong>Can Access D-Ring Road (loc1):</strong> {debugInfo.canAccessDRing ? 'Yes' : 'No'}</p>
            <p><strong>Can Access Muaither (loc2):</strong> {debugInfo.canAccessMuaither ? 'Yes' : 'No'}</p>
            <p><strong>Can Access All:</strong> {debugInfo.canAccessAll ? 'Yes' : 'No'}</p>
            <p><strong>User Accessible Locations:</strong> {debugInfo.userAccessibleLocations?.join(', ')}</p>
          </div>
        </div>

        <div className="space-y-4">
          <h2 className="text-xl font-semibold">All Locations</h2>
          <div className="bg-gray-100 p-4 rounded">
            <pre className="text-sm overflow-auto">
              {JSON.stringify(debugInfo.allLocations, null, 2)}
            </pre>
          </div>
        </div>

        <div className="space-y-4">
          <h2 className="text-xl font-semibold">Active Locations</h2>
          <div className="bg-gray-100 p-4 rounded">
            <pre className="text-sm overflow-auto">
              {JSON.stringify(debugInfo.activeLocations, null, 2)}
            </pre>
          </div>
        </div>

        <div className="space-y-4">
          <h2 className="text-xl font-semibold">All Staff</h2>
          <div className="bg-gray-100 p-4 rounded">
            <pre className="text-sm overflow-auto">
              {JSON.stringify(debugInfo.allStaff, null, 2)}
            </pre>
          </div>
        </div>

        <div className="space-y-4">
          <h2 className="text-xl font-semibold">D-Ring Road Staff</h2>
          <div className="bg-gray-100 p-4 rounded">
            <pre className="text-sm overflow-auto">
              {JSON.stringify(debugInfo.dRingStaff, null, 2)}
            </pre>
          </div>
        </div>

        <div className="space-y-4 md:col-span-2">
          <h2 className="text-xl font-semibold">LocalStorage Data</h2>
          <div className="bg-gray-100 p-4 rounded">
            <div className="space-y-2">
              <div>
                <strong>User:</strong>
                <pre className="text-sm overflow-auto">{debugInfo.localStorage?.user}</pre>
              </div>
              <div>
                <strong>Location:</strong>
                <pre className="text-sm overflow-auto">{debugInfo.localStorage?.location}</pre>
              </div>
              <div>
                <strong>Staff:</strong>
                <pre className="text-sm overflow-auto">{debugInfo.localStorage?.staff}</pre>
              </div>
              <div>
                <strong>Locations:</strong>
                <pre className="text-sm overflow-auto">{debugInfo.localStorage?.locations}</pre>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
