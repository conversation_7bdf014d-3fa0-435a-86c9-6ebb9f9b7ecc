import { NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"

/**
 * POST /api/assign-specific-staff
 * 
 * Assigns specific staff members to Medinat Khalifa without affecting others
 */
export async function POST() {
  try {
    console.log("🔧 Assigning specific staff to Medinat Khalifa...")

    // The 3 staff members that need to be assigned to Medinat Khalifa
    const staffToAssign = ["Maria Santos", "<PERSON><PERSON><PERSON>e", "<PERSON>k<PERSON>"]

    let assignmentCount = 0
    const assignedStaff: string[] = []

    for (const staffName of staffToAssign) {
      // Find the staff member
      const staffMember = await prisma.staffMember.findFirst({
        where: {
          name: {
            contains: staffName
          },
          status: 'ACTIVE'
        }
      })

      if (staffMember) {
        // Check if they already have a location assignment
        const existingAssignment = await prisma.staffLocation.findFirst({
          where: {
            staffId: staffMember.id,
            isActive: true
          }
        })

        if (!existingAssignment) {
          // Create new assignment to Medinat Khalifa
          await prisma.staffLocation.create({
            data: {
              staffId: staffMember.id,
              locationId: 'loc3',
              isActive: true
            }
          })
          console.log(`✅ Assigned ${staffMember.name} to Medinat Khalifa (loc3)`)
          assignmentCount++
          assignedStaff.push(staffMember.name)
        } else {
          console.log(`ℹ️ ${staffMember.name} already has a location assignment`)
        }
      } else {
        console.warn(`⚠️ Staff member not found: ${staffName}`)
      }
    }

    // Get updated counts
    const finalCounts = await Promise.all([
      prisma.staffLocation.count({ where: { locationId: 'loc1', isActive: true } }),
      prisma.staffLocation.count({ where: { locationId: 'loc2', isActive: true } }),
      prisma.staffLocation.count({ where: { locationId: 'loc3', isActive: true } })
    ])

    console.log("✅ Assignment completed!")
    console.log(`📊 Updated distribution:`)
    console.log(`   D-ring road (loc1): ${finalCounts[0]} staff`)
    console.log(`   Muaither (loc2): ${finalCounts[1]} staff`)
    console.log(`   Medinat Khalifa (loc3): ${finalCounts[2]} staff`)

    return NextResponse.json({
      success: true,
      message: `Successfully assigned ${assignmentCount} staff members to Medinat Khalifa`,
      summary: {
        assignedStaff,
        totalAssignments: assignmentCount,
        locationCounts: {
          "D-ring road (loc1)": finalCounts[0],
          "Muaither (loc2)": finalCounts[1],
          "Medinat Khalifa (loc3)": finalCounts[2]
        }
      }
    })

  } catch (error) {
    console.error("❌ Error during staff assignment:", error)
    return NextResponse.json({
      success: false,
      error: "Failed to assign specific staff",
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 })
  }
}
