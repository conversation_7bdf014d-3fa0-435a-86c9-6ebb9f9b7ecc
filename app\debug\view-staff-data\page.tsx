"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

export default function ViewStaffDataPage() {
  const [staffData, setStaffData] = useState<any>(null)
  const [selectedLocation, setSelectedLocation] = useState<string>("loc1")
  const [isLoading, setIsLoading] = useState(false)

  const fetchStaffData = async (locationId: string) => {
    setIsLoading(true)
    try {
      const response = await fetch(`/api/staff?locationId=${locationId}`)
      const data = await response.json()
      setStaffData(data)
      console.log(`Staff data for ${locationId}:`, data)
    } catch (error) {
      console.error("Error fetching staff data:", error)
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    fetchStaffData(selectedLocation)
  }, [selectedLocation])

  const locations = [
    { id: "all", name: "All Locations" },
    { id: "loc1", name: "D-ring road" },
    { id: "loc2", name: "Muaither" },
    { id: "loc3", name: "Medinat Khalifa" },
    { id: "home", name: "Home Service" },
    { id: "online", name: "Online" }
  ]

  return (
    <div className="p-6 space-y-6">
      <h1 className="text-2xl font-bold">Staff Data Viewer</h1>
      
      <div className="flex gap-4 items-center">
        <Select value={selectedLocation} onValueChange={setSelectedLocation}>
          <SelectTrigger className="w-[200px]">
            <SelectValue placeholder="Select location" />
          </SelectTrigger>
          <SelectContent>
            {locations.map((location) => (
              <SelectItem key={location.id} value={location.id}>
                {location.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        
        <Button onClick={() => fetchStaffData(selectedLocation)} disabled={isLoading}>
          {isLoading ? "Loading..." : "Refresh Data"}
        </Button>
      </div>

      {staffData && (
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Staff Data Summary</CardTitle>
              <CardDescription>
                Location: {locations.find(loc => loc.id === selectedLocation)?.name}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="p-3 bg-blue-50 rounded-lg">
                  <p className="text-sm text-gray-600">Total Staff</p>
                  <p className="text-2xl font-bold text-blue-600">
                    {staffData.staff?.length || 0}
                  </p>
                </div>
                <div className="p-3 bg-green-50 rounded-lg">
                  <p className="text-sm text-gray-600">Active Staff</p>
                  <p className="text-2xl font-bold text-green-600">
                    {staffData.staff?.filter((s: any) => s.status === 'ACTIVE').length || 0}
                  </p>
                </div>
                <div className="p-3 bg-purple-50 rounded-lg">
                  <p className="text-sm text-gray-600">Stylists</p>
                  <p className="text-2xl font-bold text-purple-600">
                    {staffData.staff?.filter((s: any) => s.role === 'stylist').length || 0}
                  </p>
                </div>
                <div className="p-3 bg-orange-50 rounded-lg">
                  <p className="text-sm text-gray-600">Receptionists</p>
                  <p className="text-2xl font-bold text-orange-600">
                    {staffData.staff?.filter((s: any) => s.role === 'receptionist').length || 0}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Staff List</CardTitle>
              <CardDescription>
                {staffData.staff?.length || 0} staff members found
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4 max-h-96 overflow-y-auto">
                {staffData.staff?.map((staff: any, index: number) => (
                  <div key={staff.id || index} className="p-4 border rounded-lg">
                    <div className="flex justify-between items-start">
                      <div className="space-y-2">
                        <h3 className="font-semibold">{staff.name}</h3>
                        <div className="flex gap-2">
                          <Badge variant={staff.status === 'ACTIVE' ? 'default' : 'secondary'}>
                            {staff.status}
                          </Badge>
                          <Badge variant="outline">
                            {staff.role}
                          </Badge>
                        </div>
                        <div className="text-sm text-gray-600">
                          <p><strong>Email:</strong> {staff.email || 'N/A'}</p>
                          <p><strong>Phone:</strong> {staff.phone || 'N/A'}</p>
                          <p><strong>Locations:</strong> {staff.locations?.join(', ') || 'None'}</p>
                          <p><strong>Home Service:</strong> {staff.homeService ? 'Yes' : 'No'}</p>
                        </div>
                      </div>
                      <div className="text-right text-sm text-gray-500">
                        <p>ID: {staff.id}</p>
                        {staff.userId && <p>User ID: {staff.userId}</p>}
                      </div>
                    </div>
                  </div>
                )) || <p>No staff data available</p>}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Raw API Response</CardTitle>
              <CardDescription>
                Full API response for debugging
              </CardDescription>
            </CardHeader>
            <CardContent>
              <pre className="bg-gray-100 p-4 rounded-lg text-xs overflow-auto max-h-60">
                {JSON.stringify(staffData, null, 2)}
              </pre>
            </CardContent>
          </Card>
        </div>
      )}

      <div className="space-y-4">
        <h2 className="text-xl font-semibold">Quick Actions</h2>
        <div className="flex gap-4">
          <Button onClick={() => window.location.href = '/dashboard/appointments'}>
            Go to Calendar
          </Button>
          <Button onClick={() => window.location.href = '/debug/calendar-location-debug'} variant="outline">
            Calendar Debug
          </Button>
          <Button onClick={() => window.location.href = '/debug/fix-staff-locations'} variant="outline">
            Fix Staff Locations
          </Button>
        </div>
      </div>

      <div className="space-y-4">
        <h2 className="text-xl font-semibold">Expected Results</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card>
            <CardHeader>
              <CardTitle>D-ring road (loc1)</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm">Should show:</p>
              <ul className="text-xs space-y-1 mt-2">
                <li>• Mekdes Abebe (stylist)</li>
                <li>• Fatima Al-Zahra (stylist)</li>
                <li>• Layla Hassan (stylist)</li>
                <li>• Nour Al-Din (stylist)</li>
                <li>• Amina Khoury (stylist)</li>
                <li>• Yasmin Al-Rashid (stylist)</li>
                <li>• Zara Mansouri (stylist)</li>
                <li>• Sarah Al-Rashid (receptionist)</li>
              </ul>
              <p className="text-sm font-medium mt-2">Expected: 7-8 staff</p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle>Muaither (loc2)</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm">Should show:</p>
              <ul className="text-xs space-y-1 mt-2">
                <li>• Hala Qasemi (stylist)</li>
                <li>• Rania Khalil (stylist)</li>
                <li>• Dina Al-Kuwari (stylist)</li>
                <li>• Mariam Farouk (stylist)</li>
                <li>• Leila Badawi (stylist)</li>
                <li>• Salma Al-Thani (stylist)</li>
                <li>• Nadia Hamdan (stylist)</li>
                <li>• Fatima Al-Kuwari (receptionist)</li>
              </ul>
              <p className="text-sm font-medium mt-2">Expected: 7-8 staff</p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle>Medinat Khalifa (loc3)</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm">Should show:</p>
              <ul className="text-xs space-y-1 mt-2">
                <li>• Amal Darwish (stylist)</li>
                <li>• Rana Al-Maktoum (stylist)</li>
                <li>• Lina Sabbagh (stylist)</li>
                <li>• Maya Al-Ansari (stylist)</li>
                <li>• Huda Nassar (stylist)</li>
                <li>• Sahar Al-Mahmoud (stylist)</li>
                <li>• Aisha Al-Thani (receptionist)</li>
              </ul>
              <p className="text-sm font-medium mt-2">Expected: 6-7 staff</p>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
