"use client"

import * as React from "react"
import { useAuth } from "@/lib/auth-provider"
import { useLocations } from "@/lib/location-provider"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

export function LocationSelector() {
  const { currentLocation, setCurrentLocation, canAccessLocation, getUserAccessibleLocations, user } = useAuth()
  const { locations, getLocationName, isHomeServiceEnabled, getActiveLocations } = useLocations()

  // Debug logging
  console.log("🔍 LocationSelector - Current user:", user)
  console.log("🔍 LocationSelector - Current location:", currentLocation)
  console.log("🔍 LocationSelector - User accessible locations:", getUserAccessibleLocations())
  console.log("🔍 LocationSelector - Can access 'all':", canAccessLocation("all"))

  // Memoize the filtered locations to prevent unnecessary re-renders
  const filteredLocations = React.useMemo(() => {
    return locations
      .filter(location => location.status === "Active" && location.name && location.name.trim() !== "")
      .filter(location => !!location.id) // Filter out locations without a valid ID
      .filter(location => canAccessLocation(location.id)); // Only show locations user can access
  }, [locations, canAccessLocation]);

  // Get all active locations including special ones (online, home) with deduplication
  const allActiveLocations = React.useMemo(() => {
    const activeLocations = getActiveLocations()
      .filter(location => location.name && location.name.trim() !== "")
      .filter(location => !!location.id);

    // Remove duplicates based on name (keep the first occurrence)
    // This handles cases where database has duplicate locations with different IDs but same names
    const uniqueActiveLocations = activeLocations.filter((location, index, array) =>
      array.findIndex(loc => loc.name.toLowerCase().trim() === location.name.toLowerCase().trim()) === index
    );

    // Log warning if duplicates were found and removed
    if (activeLocations.length !== uniqueActiveLocations.length) {
      console.warn('⚠️ LocationSelector - Removed duplicate locations by name:',
        activeLocations.length - uniqueActiveLocations.length);
      console.warn('⚠️ LocationSelector - Duplicate locations found:',
        activeLocations.filter((location, index, array) =>
          array.findIndex(loc => loc.name.toLowerCase().trim() === location.name.toLowerCase().trim()) !== index
        ).map(loc => ({ id: loc.id, name: loc.name }))
      );
    }

    return uniqueActiveLocations;
  }, [getActiveLocations]);

  const handleLocationChange = React.useCallback((newLocation: string) => {
    setCurrentLocation(newLocation)
  }, [setCurrentLocation]);

  // Memoize the entire select component to prevent unnecessary re-renders
  const selectComponent = React.useMemo(() => {
    // Define the desired order for locations
    const locationOrder = [
      "muaither",
      "medinat khalifa",
      "d-ring road",
      "home",
      "online"
    ]

    // Separate regular locations from special locations
    const regularLocations = allActiveLocations.filter(loc =>
      loc.id !== "home" && loc.id !== "online"
    );
    const onlineLocation = allActiveLocations.find(loc => loc.id === "online");
    const homeLocation = allActiveLocations.find(loc => loc.id === "home");

    // Remove duplicates from regular locations before sorting (by name)
    const uniqueRegularLocations = regularLocations.filter((location, index, array) =>
      array.findIndex(loc => loc.name.toLowerCase().trim() === location.name.toLowerCase().trim()) === index
    );

    // Sort regular locations according to the desired order
    const sortedRegularLocations = uniqueRegularLocations.sort((a, b) => {
      const aIndex = locationOrder.findIndex(name =>
        a.name.toLowerCase().includes(name) || name.includes(a.name.toLowerCase())
      );
      const bIndex = locationOrder.findIndex(name =>
        b.name.toLowerCase().includes(name) || name.includes(b.name.toLowerCase())
      );

      // If both locations are in the order array, sort by their position
      if (aIndex !== -1 && bIndex !== -1) {
        return aIndex - bIndex;
      }
      // If only one is in the order array, prioritize it
      if (aIndex !== -1) return -1;
      if (bIndex !== -1) return 1;
      // If neither is in the order array, maintain alphabetical order
      return a.name.localeCompare(b.name);
    });

    return (
      <Select
        value={currentLocation || "all"}
        onValueChange={handleLocationChange}
      >
        <SelectTrigger className="w-[180px] bg-muted/50 border-0">
          <SelectValue placeholder="Select location" />
        </SelectTrigger>
        <SelectContent>
          {/* Show "All Locations" option for all users */}
          <SelectItem value="all">All Locations</SelectItem>

          {/* Map through regular active locations in custom order */}
          {sortedRegularLocations.map(location => (
            <SelectItem key={location.id} value={location.id}>
              📍 {location.name}
            </SelectItem>
          ))}

          {/* Add Home Service option if enabled and exists and user has access */}
          {isHomeServiceEnabled && homeLocation && canAccessLocation("home") && (
            <SelectItem value="home" key="home">
              🏠 Home Service
            </SelectItem>
          )}

          {/* Add Online Store option after Home Service if user has access */}
          {onlineLocation && canAccessLocation("online") && (
            <SelectItem value="online" key="online">
              🌐 Online Store
            </SelectItem>
          )}
        </SelectContent>
      </Select>
    );
  }, [currentLocation, handleLocationChange, allActiveLocations, isHomeServiceEnabled]);

  return selectComponent;
}

