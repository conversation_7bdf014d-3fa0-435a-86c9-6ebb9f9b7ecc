"use client"

import { useState } from "react"
import { StockAdjustmentDialog } from "@/components/inventory/stock-adjustment-dialog"
import { Button } from "@/components/ui/button"

export default function TestStockAdjustmentPage() {
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  
  // Sample product with limited stock to test validation
  const sampleProduct = {
    id: "test-product-1",
    name: "Test Product with Limited Stock",
    stock: 15, // Only 15 units available
    price: 50.00,
    cost: 25.00,
    category: "HAIR_CARE",
    locationId: "loc1"
  }

  const handleStockAdjusted = async () => {
    console.log("✅ Stock adjustment completed - refreshing data...")
    // In a real app, this would refresh the inventory data
  }

  return (
    <div className="p-6 space-y-6">
      <h1 className="text-2xl font-bold">Test Stock Adjustment Dialog</h1>
      
      <div className="space-y-4">
        <div className="bg-blue-50 p-4 rounded-lg">
          <h2 className="text-lg font-semibold mb-2">Improvements Made:</h2>
          <ul className="list-disc list-inside space-y-1 text-sm">
            <li>✅ <strong>Client-side validation</strong>: Prevents removing more stock than available</li>
            <li>✅ <strong>Dynamic max quantity</strong>: Input field shows maximum available stock</li>
            <li>✅ <strong>Visual feedback</strong>: Shows warning when quantity exceeds available stock</li>
            <li>✅ <strong>Disabled submit button</strong>: Button is disabled when form is invalid</li>
            <li>✅ <strong>Better error messages</strong>: User-friendly error messages from API</li>
            <li>✅ <strong>Real-time validation</strong>: Form validates as user types</li>
          </ul>
        </div>

        <div className="bg-gray-100 p-4 rounded-lg">
          <h3 className="font-semibold mb-2">Test Product Details:</h3>
          <p><strong>Name:</strong> {sampleProduct.name}</p>
          <p><strong>Current Stock:</strong> {sampleProduct.stock} units</p>
          <p><strong>Price:</strong> QAR {sampleProduct.price}</p>
          <p><strong>Cost:</strong> QAR {sampleProduct.cost}</p>
        </div>

        <div className="space-y-2">
          <h3 className="font-semibold">Test Scenarios:</h3>
          <div className="bg-yellow-50 p-3 rounded text-sm">
            <p><strong>1. Valid Addition:</strong> Try adding 10 units - should work normally</p>
            <p><strong>2. Valid Removal:</strong> Try removing 5 units - should work normally</p>
            <p><strong>3. Invalid Removal:</strong> Try removing 20 units (more than 15 available) - should show validation error and disable submit</p>
            <p><strong>4. Edge Case:</strong> Try removing exactly 15 units - should work (removes all stock)</p>
          </div>
        </div>

        <Button 
          onClick={() => setIsDialogOpen(true)}
          className="bg-blue-600 hover:bg-blue-700"
        >
          Open Stock Adjustment Dialog
        </Button>
      </div>

      <div className="space-y-4">
        <h2 className="text-xl font-semibold">Error Prevention Features:</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="bg-green-50 p-4 rounded-lg">
            <h3 className="font-semibold text-green-800 mb-2">Before (Issues):</h3>
            <ul className="list-disc list-inside space-y-1 text-sm text-green-700">
              <li>❌ Users could enter any quantity</li>
              <li>❌ No validation until API call</li>
              <li>❌ Confusing error messages</li>
              <li>❌ Console errors for insufficient stock</li>
              <li>❌ Poor user experience</li>
            </ul>
          </div>
          
          <div className="bg-blue-50 p-4 rounded-lg">
            <h3 className="font-semibold text-blue-800 mb-2">After (Fixed):</h3>
            <ul className="list-disc list-inside space-y-1 text-sm text-blue-700">
              <li>✅ Real-time quantity validation</li>
              <li>✅ Visual feedback for invalid inputs</li>
              <li>✅ Clear error messages</li>
              <li>✅ Prevents invalid API calls</li>
              <li>✅ Better user experience</li>
            </ul>
          </div>
        </div>
      </div>

      <div className="space-y-4">
        <h2 className="text-xl font-semibold">Technical Details:</h2>
        <div className="bg-gray-50 p-4 rounded-lg text-sm">
          <p><strong>Client-side Validation:</strong> Checks quantity against available stock before allowing submission</p>
          <p><strong>Dynamic UI:</strong> Input field shows max available stock and updates validation in real-time</p>
          <p><strong>Error Handling:</strong> API errors are parsed and displayed with user-friendly messages</p>
          <p><strong>Form State:</strong> Submit button is disabled when form is invalid to prevent errors</p>
        </div>
      </div>

      <StockAdjustmentDialog
        open={isDialogOpen}
        onOpenChange={setIsDialogOpen}
        product={sampleProduct}
        onStockAdjusted={handleStockAdjusted}
      />
    </div>
  )
}
