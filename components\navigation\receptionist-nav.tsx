"use client"

import { useAuth } from "@/lib/auth-provider"
import { usePathname } from "next/navigation"
import Link from "next/link"
import { cn } from "@/lib/utils"
import { 
  Calendar, 
  Users, 
  UserPlus, 
  Clock, 
  Package,
  FileText,
  Settings,
  LogOut
} from "lucide-react"

interface NavItem {
  name: string
  href: string
  icon: React.ComponentType<{ className?: string }>
  description: string
}

const receptionistNavItems: NavItem[] = [
  {
    name: "Appointments",
    href: "/dashboard/appointments",
    icon: Calendar,
    description: "View and manage appointments"
  },
  {
    name: "Clients",
    href: "/dashboard/clients",
    icon: Users,
    description: "Manage client information"
  },
  {
    name: "New Appointment",
    href: "/dashboard/appointments/new",
    icon: UserPlus,
    description: "Book new appointment"
  },
  {
    name: "Schedule",
    href: "/dashboard/schedule",
    icon: Clock,
    description: "View staff schedules"
  },
  {
    name: "Services",
    href: "/dashboard/services",
    icon: Package,
    description: "View services and pricing"
  },
  {
    name: "Reports",
    href: "/dashboard/reports",
    icon: FileText,
    description: "Basic reports for your location"
  }
]

export function ReceptionistNav() {
  const { user, logout, currentLocation } = useAuth()
  const pathname = usePathname()

  if (!user || (user.role !== 'RECEPTIONIST' && user.role !== 'receptionist')) {
    return null
  }

  // Get the location name for display
  const getLocationName = () => {
    if (currentLocation === 'loc1') return 'D-ring road'
    if (currentLocation === 'loc2') return 'Muaither'
    if (currentLocation === 'loc3') return 'Medinat Khalifa'
    return currentLocation
  }

  return (
    <div className="bg-white shadow-sm border-b">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center py-4">
          {/* Logo and Location */}
          <div className="flex items-center space-x-4">
            <div className="flex items-center">
              <div className="w-8 h-8 bg-purple-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">V</span>
              </div>
              <span className="ml-2 text-xl font-bold text-gray-900">Vanity Hub</span>
            </div>
            <div className="hidden md:block">
              <span className="text-sm text-gray-500">|</span>
              <span className="ml-2 text-sm font-medium text-gray-700">
                {getLocationName()}
              </span>
            </div>
          </div>

          {/* Navigation Items */}
          <nav className="hidden md:flex space-x-1">
            {receptionistNavItems.map((item) => {
              const isActive = pathname === item.href || pathname.startsWith(item.href + '/')
              return (
                <Link
                  key={item.name}
                  href={item.href}
                  className={cn(
                    "px-3 py-2 rounded-md text-sm font-medium transition-colors",
                    isActive
                      ? "bg-purple-100 text-purple-700"
                      : "text-gray-600 hover:text-gray-900 hover:bg-gray-50"
                  )}
                  title={item.description}
                >
                  <item.icon className="w-4 h-4 inline mr-2" />
                  {item.name}
                </Link>
              )
            })}
          </nav>

          {/* User Menu */}
          <div className="flex items-center space-x-4">
            <div className="hidden md:block text-right">
              <div className="text-sm font-medium text-gray-900">{user.name}</div>
              <div className="text-xs text-gray-500">Receptionist</div>
            </div>
            <button
              onClick={logout}
              className="flex items-center px-3 py-2 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-md transition-colors"
              title="Sign out"
            >
              <LogOut className="w-4 h-4 mr-2" />
              <span className="hidden sm:inline">Sign out</span>
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        <div className="md:hidden pb-4">
          <div className="flex flex-wrap gap-2">
            {receptionistNavItems.map((item) => {
              const isActive = pathname === item.href || pathname.startsWith(item.href + '/')
              return (
                <Link
                  key={item.name}
                  href={item.href}
                  className={cn(
                    "flex items-center px-3 py-2 rounded-md text-sm font-medium transition-colors",
                    isActive
                      ? "bg-purple-100 text-purple-700"
                      : "text-gray-600 hover:text-gray-900 hover:bg-gray-50"
                  )}
                >
                  <item.icon className="w-4 h-4 mr-2" />
                  {item.name}
                </Link>
              )
            })}
          </div>
          
          {/* Mobile Location Display */}
          <div className="mt-3 pt-3 border-t border-gray-200">
            <div className="text-sm text-gray-600">
              <span className="font-medium">Location:</span> {getLocationName()}
            </div>
            <div className="text-sm text-gray-600">
              <span className="font-medium">User:</span> {user.name} (Receptionist)
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

// Restricted pages notice for receptionists
export function ReceptionistRestrictedNotice({ 
  pageName, 
  reason = "This page is restricted for receptionist users." 
}: { 
  pageName: string
  reason?: string 
}) {
  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <div className="max-w-md w-full bg-white shadow-lg rounded-lg p-6">
        <div className="text-center">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <Settings className="w-8 h-8 text-red-600" />
          </div>
          <h1 className="text-xl font-bold text-gray-900 mb-2">Access Restricted</h1>
          <p className="text-gray-600 mb-4">
            You don't have permission to access <strong>{pageName}</strong>.
          </p>
          <p className="text-sm text-gray-500 mb-6">{reason}</p>
          <div className="space-y-2">
            <Link
              href="/dashboard/appointments"
              className="block w-full bg-purple-600 text-white py-2 px-4 rounded-md hover:bg-purple-700 transition-colors"
            >
              Go to Appointments
            </Link>
            <Link
              href="/dashboard"
              className="block w-full bg-gray-100 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-200 transition-colors"
            >
              Go to Dashboard
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}
