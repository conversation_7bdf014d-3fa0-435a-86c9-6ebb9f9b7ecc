import { NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"

export async function POST() {
  try {
    console.log("🔧 Starting complete D-Ring Road setup...")

    // 1. Ensure D-Ring Road location exists
    console.log("1️⃣ Setting up D-Ring Road location...")
    
    let dRingLocation = await prisma.location.findFirst({
      where: {
        OR: [
          { id: 'loc1' },
          { name: { contains: 'D-ring', mode: 'insensitive' } }
        ]
      }
    })

    if (!dRingLocation) {
      console.log("Creating D-Ring Road location...")
      dRingLocation = await prisma.location.create({
        data: {
          id: 'loc1',
          name: 'D-ring road',
          address: '123 D-Ring Road',
          city: 'Doha',
          state: 'Doha',
          zipCode: '12345',
          country: 'Qatar',
          phone: '(*************',
          email: '<EMAIL>',
          isActive: true
        }
      })
      console.log("✅ D-Ring Road location created")
    } else {
      // Ensure it's active
      if (!dRingLocation.isActive) {
        dRingLocation = await prisma.location.update({
          where: { id: dRingLocation.id },
          data: { isActive: true }
        })
        console.log("✅ D-Ring Road location activated")
      } else {
        console.log("✅ D-Ring Road location already exists and is active")
      }
    }

    // 2. Ensure Mekdes Abebe user exists
    console.log("2️⃣ Setting up Mekdes Abebe user...")
    
    let mekdesUser = await prisma.user.findFirst({
      where: {
        email: '<EMAIL>'
      }
    })

    if (!mekdesUser) {
      console.log("Creating Mekdes Abebe user...")
      mekdesUser = await prisma.user.create({
        data: {
          name: 'Mekdes Abebe',
          email: '<EMAIL>',
          password: 'hashed_password_placeholder', // In real app, this would be properly hashed
          role: 'STAFF',
          isActive: true
        }
      })
      console.log("✅ Mekdes Abebe user created")
    } else {
      console.log("✅ Mekdes Abebe user already exists")
    }

    // 3. Ensure Mekdes staff member exists and is assigned to D-Ring Road
    console.log("3️⃣ Setting up Mekdes Abebe staff member...")
    
    let mekdesStaff = await prisma.staffMember.findFirst({
      where: {
        userId: mekdesUser.id
      },
      include: {
        locations: {
          include: {
            location: true
          }
        }
      }
    })

    if (!mekdesStaff) {
      console.log("Creating Mekdes Abebe staff member...")
      mekdesStaff = await prisma.staffMember.create({
        data: {
          userId: mekdesUser.id,
          name: 'Mekdes Abebe',
          phone: '+974 5555 1234',
          avatar: 'MA',
          color: 'bg-purple-100 text-purple-800',
          homeService: false,
          status: 'ACTIVE',
          jobRole: 'stylist'
        },
        include: {
          locations: {
            include: {
              location: true
            }
          }
        }
      })
      console.log("✅ Mekdes Abebe staff member created")
    } else {
      console.log("✅ Mekdes Abebe staff member already exists")
    }

    // 4. Ensure Mekdes is assigned to D-Ring Road location
    console.log("4️⃣ Assigning Mekdes to D-Ring Road location...")
    
    const existingAssignment = await prisma.staffLocation.findFirst({
      where: {
        staffId: mekdesStaff.id,
        locationId: dRingLocation.id
      }
    })

    if (!existingAssignment) {
      await prisma.staffLocation.create({
        data: {
          staffId: mekdesStaff.id,
          locationId: dRingLocation.id,
          isActive: true
        }
      })
      console.log("✅ Mekdes assigned to D-Ring Road location")
    } else {
      // Ensure assignment is active
      if (!existingAssignment.isActive) {
        await prisma.staffLocation.update({
          where: { id: existingAssignment.id },
          data: { isActive: true }
        })
        console.log("✅ Mekdes assignment to D-Ring Road activated")
      } else {
        console.log("✅ Mekdes already assigned to D-Ring Road")
      }
    }

    // 5. Create sample appointments for D-Ring Road
    console.log("5️⃣ Creating sample appointments...")
    
    const today = new Date()
    const tomorrow = new Date(today)
    tomorrow.setDate(today.getDate() + 1)
    
    const existingAppointments = await prisma.appointment.findMany({
      where: {
        locationId: dRingLocation.id,
        date: {
          gte: today
        }
      }
    })

    if (existingAppointments.length === 0) {
      // Create sample appointments
      const sampleAppointments = [
        {
          clientName: "Sarah Ahmed",
          clientEmail: "<EMAIL>",
          clientPhone: "+974 5555 0001",
          serviceName: "Hair Cut & Style",
          staffId: mekdesStaff.id,
          locationId: dRingLocation.id,
          date: tomorrow,
          startTime: "10:00",
          endTime: "11:00",
          duration: 60,
          price: 150,
          status: "CONFIRMED",
          notes: "Regular client, prefers layered cut"
        },
        {
          clientName: "Fatima Al-Rashid",
          clientEmail: "<EMAIL>",
          clientPhone: "+974 5555 0002",
          serviceName: "Hair Color",
          staffId: mekdesStaff.id,
          locationId: dRingLocation.id,
          date: tomorrow,
          startTime: "14:00",
          endTime: "16:00",
          duration: 120,
          price: 250,
          status: "PENDING",
          notes: "First time color, consultation needed"
        }
      ]

      for (const appointment of sampleAppointments) {
        await prisma.appointment.create({
          data: appointment
        })
      }
      
      console.log(`✅ Created ${sampleAppointments.length} sample appointments`)
    } else {
      console.log(`✅ ${existingAppointments.length} appointments already exist for D-Ring Road`)
    }

    // 6. Verify the setup
    console.log("6️⃣ Verifying setup...")
    
    const verification = {
      location: await prisma.location.findUnique({
        where: { id: dRingLocation.id }
      }),
      staff: await prisma.staffMember.findUnique({
        where: { id: mekdesStaff.id },
        include: {
          locations: {
            include: {
              location: true
            }
          }
        }
      }),
      appointments: await prisma.appointment.count({
        where: {
          locationId: dRingLocation.id,
          date: {
            gte: today
          }
        }
      })
    }

    console.log("✅ Setup verification complete")
    console.log(`   - Location: ${verification.location?.name} (Active: ${verification.location?.isActive})`)
    console.log(`   - Staff: ${verification.staff?.name} (Status: ${verification.staff?.status})`)
    console.log(`   - Staff Locations: ${verification.staff?.locations.map(l => l.location.name).join(', ')}`)
    console.log(`   - Appointments: ${verification.appointments}`)

    return NextResponse.json({
      success: true,
      message: "D-Ring Road setup completed successfully",
      data: {
        location: {
          id: dRingLocation.id,
          name: dRingLocation.name,
          isActive: dRingLocation.isActive
        },
        staff: {
          id: mekdesStaff.id,
          name: mekdesStaff.name,
          status: mekdesStaff.status,
          locations: verification.staff?.locations.map(l => ({
            id: l.location.id,
            name: l.location.name
          }))
        },
        appointmentCount: verification.appointments
      }
    })

  } catch (error) {
    console.error("❌ Error setting up D-Ring Road:", error)
    return NextResponse.json({
      success: false,
      error: "Failed to setup D-Ring Road",
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 })
  }
}
