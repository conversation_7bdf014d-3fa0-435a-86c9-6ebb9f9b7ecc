import { NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"

/**
 * POST /api/assign-remaining-staff
 * 
 * Assigns remaining staff without locations to Medinat Khalifa (loc3)
 */
export async function POST() {
  try {
    console.log("🔧 Assigning remaining staff to Medinat Khalifa...")

    // Get staff members without location assignments
    const staffWithoutLocations = await prisma.staffMember.findMany({
      where: {
        status: 'ACTIVE',
        locations: {
          none: {}
        }
      }
    })

    console.log(`Found ${staffWithoutLocations.length} staff members without locations:`)
    staffWithoutLocations.forEach(staff => console.log(`  - ${staff.name} (${staff.role})`))

    let assignmentCount = 0

    // Assign each staff member to Medinat Khalifa (loc3)
    for (const staff of staffWithoutLocations) {
      await prisma.staffLocation.create({
        data: {
          staffId: staff.id,
          locationId: 'loc3',
          isActive: true
        }
      })
      console.log(`✅ Assigned ${staff.name} to Medinat Khalifa (loc3)`)
      assignmentCount++
    }

    // Get updated counts
    const finalCounts = await Promise.all([
      prisma.staffLocation.count({ where: { locationId: 'loc1', isActive: true } }),
      prisma.staffLocation.count({ where: { locationId: 'loc2', isActive: true } }),
      prisma.staffLocation.count({ where: { locationId: 'loc3', isActive: true } })
    ])

    console.log("✅ Assignment completed!")
    console.log(`📊 Updated distribution:`)
    console.log(`   D-ring road (loc1): ${finalCounts[0]} staff`)
    console.log(`   Muaither (loc2): ${finalCounts[1]} staff`)
    console.log(`   Medinat Khalifa (loc3): ${finalCounts[2]} staff`)

    return NextResponse.json({
      success: true,
      message: `Successfully assigned ${assignmentCount} staff members to Medinat Khalifa`,
      summary: {
        assignedStaff: staffWithoutLocations.map(s => s.name),
        totalAssignments: assignmentCount,
        locationCounts: {
          "D-ring road (loc1)": finalCounts[0],
          "Muaither (loc2)": finalCounts[1],
          "Medinat Khalifa (loc3)": finalCounts[2]
        }
      }
    })

  } catch (error) {
    console.error("❌ Error during staff assignment:", error)
    return NextResponse.json({
      success: false,
      error: "Failed to assign remaining staff",
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 })
  }
}
