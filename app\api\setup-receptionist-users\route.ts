import { NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"

export async function POST() {
  try {
    console.log("👥 Starting receptionist users setup...")

    // First, ensure all locations exist
    const locations = [
      {
        id: 'loc1',
        name: 'D-ring road',
        address: '123 D-Ring Road',
        city: 'Doha',
        state: 'Doha',
        zipCode: '12345',
        country: 'Qatar',
        phone: '(*************',
        email: '<EMAIL>',
        isActive: true
      },
      {
        id: 'loc2',
        name: '<PERSON><PERSON><PERSON>',
        address: '456 Muaither Street',
        city: 'Doha',
        state: 'Doha',
        zipCode: '23456',
        country: 'Qatar',
        phone: '(*************',
        email: '<EMAIL>',
        isActive: true
      },
      {
        id: 'loc3',
        name: 'Medinat Khalifa',
        address: '789 Medinat Khalifa Avenue',
        city: 'Doha',
        state: 'Doha',
        zipCode: '34567',
        country: 'Qatar',
        phone: '(*************',
        email: '<EMAIL>',
        isActive: true
      }
    ]

    console.log("📍 Creating/updating locations...")
    for (const location of locations) {
      await prisma.location.upsert({
        where: { id: location.id },
        update: location,
        create: location
      })
      console.log(`✅ Location: ${location.name} (${location.id})`)
    }

    // Receptionist users data
    const receptionistUsers = [
      {
        name: 'Sarah Al-Rashid',
        email: '<EMAIL>',
        password: 'receptionist123', // In production, this would be properly hashed
        role: 'RECEPTIONIST',
        locationId: 'loc1',
        locationName: 'D-ring road',
        phone: '+974 5555 1001',
        employeeNumber: 'REC001'
      },
      {
        name: 'Fatima Al-Kuwari',
        email: '<EMAIL>',
        password: 'receptionist123',
        role: 'RECEPTIONIST',
        locationId: 'loc2',
        locationName: 'Muaither',
        phone: '+974 5555 1002',
        employeeNumber: 'REC002'
      },
      {
        name: 'Aisha Al-Thani',
        email: '<EMAIL>',
        password: 'receptionist123',
        role: 'RECEPTIONIST',
        locationId: 'loc3',
        locationName: 'Medinat Khalifa',
        phone: '+974 5555 1003',
        employeeNumber: 'REC003'
      }
    ]

    console.log("👥 Creating receptionist users...")
    const createdUsers = []

    for (const userData of receptionistUsers) {
      // Check if user already exists
      let user = await prisma.user.findFirst({
        where: { email: userData.email }
      })

      if (user) {
        // Update existing user
        user = await prisma.user.update({
          where: { id: user.id },
          data: {
            name: userData.name,
            role: userData.role,
            isActive: true,
            // Set user locations for receptionists
            locations: [userData.locationId]
          }
        })
        console.log(`✅ Updated user: ${user.name} (${user.email}) with location: ${userData.locationId}`)
      } else {
        // Create new user
        user = await prisma.user.create({
          data: {
            name: userData.name,
            email: userData.email,
            password: 'hashed_password_placeholder', // In real app, properly hash this
            role: userData.role,
            isActive: true,
            // Set user locations for receptionists
            locations: [userData.locationId]
          }
        })
        console.log(`✅ Created user: ${user.name} (${user.email}) with location: ${userData.locationId}`)
      }

      // Create or update staff member
      let staffMember = await prisma.staffMember.findFirst({
        where: { userId: user.id }
      })

      if (staffMember) {
        // Update existing staff member
        staffMember = await prisma.staffMember.update({
          where: { id: staffMember.id },
          data: {
            name: userData.name,
            phone: userData.phone,
            status: 'ACTIVE',
            jobRole: 'receptionist'
          }
        })
        console.log(`✅ Updated staff member: ${staffMember.name}`)
      } else {
        // Create new staff member
        staffMember = await prisma.staffMember.create({
          data: {
            userId: user.id,
            name: userData.name,
            phone: userData.phone,
            avatar: userData.name.split(' ').map(n => n[0]).join(''),
            color: 'bg-blue-100 text-blue-800',
            homeService: false,
            status: 'ACTIVE',
            jobRole: 'receptionist'
          }
        })
        console.log(`✅ Created staff member: ${staffMember.name}`)
      }

      // Assign staff member to their specific location
      const existingAssignment = await prisma.staffLocation.findFirst({
        where: {
          staffId: staffMember.id,
          locationId: userData.locationId
        }
      })

      if (!existingAssignment) {
        await prisma.staffLocation.create({
          data: {
            staffId: staffMember.id,
            locationId: userData.locationId,
            isActive: true
          }
        })
        console.log(`✅ Assigned ${staffMember.name} to ${userData.locationName}`)
      } else {
        await prisma.staffLocation.update({
          where: { id: existingAssignment.id },
          data: { isActive: true }
        })
        console.log(`✅ Updated assignment: ${staffMember.name} to ${userData.locationName}`)
      }

      createdUsers.push({
        name: userData.name,
        email: userData.email,
        password: userData.password,
        role: userData.role,
        location: userData.locationName,
        locationId: userData.locationId,
        phone: userData.phone,
        employeeNumber: userData.employeeNumber
      })
    }

    // Create sample appointments for each location to test access control
    console.log("📅 Creating sample appointments for each location...")
    
    const today = new Date()
    const tomorrow = new Date(today)
    tomorrow.setDate(today.getDate() + 1)

    const sampleAppointments = [
      // D-ring road appointments
      {
        clientName: "Layla Ahmed",
        clientEmail: "<EMAIL>",
        clientPhone: "+974 5555 2001",
        serviceName: "Hair Cut & Style",
        locationId: "loc1",
        date: tomorrow,
        startTime: "09:00",
        endTime: "10:00",
        duration: 60,
        price: 120,
        status: "CONFIRMED"
      },
      {
        clientName: "Noor Al-Mansouri",
        clientEmail: "<EMAIL>",
        clientPhone: "+974 5555 2002",
        serviceName: "Facial Treatment",
        locationId: "loc1",
        date: tomorrow,
        startTime: "11:00",
        endTime: "12:30",
        duration: 90,
        price: 200,
        status: "PENDING"
      },
      // Muaither appointments
      {
        clientName: "Mariam Al-Kuwari",
        clientEmail: "<EMAIL>",
        clientPhone: "+974 5555 2003",
        serviceName: "Manicure & Pedicure",
        locationId: "loc2",
        date: tomorrow,
        startTime: "10:00",
        endTime: "11:30",
        duration: 90,
        price: 150,
        status: "CONFIRMED"
      },
      {
        clientName: "Hessa Al-Attiyah",
        clientEmail: "<EMAIL>",
        clientPhone: "+974 5555 2004",
        serviceName: "Hair Color",
        locationId: "loc2",
        date: tomorrow,
        startTime: "14:00",
        endTime: "16:00",
        duration: 120,
        price: 300,
        status: "CONFIRMED"
      },
      // Medinat Khalifa appointments
      {
        clientName: "Amna Al-Thani",
        clientEmail: "<EMAIL>",
        clientPhone: "+974 5555 2005",
        serviceName: "Eyebrow Threading",
        locationId: "loc3",
        date: tomorrow,
        startTime: "09:30",
        endTime: "10:00",
        duration: 30,
        price: 80,
        status: "CONFIRMED"
      },
      {
        clientName: "Sheikha Al-Naimi",
        clientEmail: "<EMAIL>",
        clientPhone: "+974 5555 2006",
        serviceName: "Full Body Massage",
        locationId: "loc3",
        date: tomorrow,
        startTime: "15:00",
        endTime: "16:30",
        duration: 90,
        price: 250,
        status: "PENDING"
      }
    ]

    for (const appointment of sampleAppointments) {
      // Check if appointment already exists
      const existingAppointment = await prisma.appointment.findFirst({
        where: {
          clientEmail: appointment.clientEmail,
          date: appointment.date,
          startTime: appointment.startTime
        }
      })

      if (!existingAppointment) {
        await prisma.appointment.create({
          data: appointment
        })
        console.log(`✅ Created appointment: ${appointment.clientName} at ${appointment.locationId}`)
      }
    }

    console.log("✅ Receptionist users setup completed!")

    return NextResponse.json({
      success: true,
      message: "Receptionist users created successfully",
      users: createdUsers,
      summary: {
        usersCreated: createdUsers.length,
        locationsSetup: locations.length,
        appointmentsCreated: sampleAppointments.length
      }
    })

  } catch (error) {
    console.error("❌ Error setting up receptionist users:", error)
    return NextResponse.json({
      success: false,
      error: "Failed to setup receptionist users",
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 })
  }
}
