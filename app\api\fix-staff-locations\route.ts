import { NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"

export async function POST() {
  try {
    console.log("🔧 Starting staff location assignments fix...")

    // First, ensure all locations exist
    const locations = [
      {
        id: 'loc1',
        name: 'D-ring road',
        address: '123 D-Ring Road',
        city: 'Doha',
        state: 'Doha',
        zipCode: '12345',
        country: 'Qatar',
        phone: '(*************',
        email: '<EMAIL>',
        isActive: true
      },
      {
        id: 'loc2',
        name: '<PERSON><PERSON><PERSON>',
        address: '456 Muaither Street',
        city: 'Doha',
        state: 'Doha',
        zipCode: '23456',
        country: 'Qatar',
        phone: '(*************',
        email: '<EMAIL>',
        isActive: true
      },
      {
        id: 'loc3',
        name: 'Medinat Khalifa',
        address: '789 Medinat Khalifa Avenue',
        city: 'Doha',
        state: 'Doha',
        zipCode: '34567',
        country: 'Qatar',
        phone: '(*************',
        email: '<EMAIL>',
        isActive: true
      }
    ]

    console.log("📍 Ensuring locations exist...")
    for (const location of locations) {
      await prisma.location.upsert({
        where: { id: location.id },
        update: location,
        create: location
      })
      console.log(`✅ Location: ${location.name} (${location.id})`)
    }

    // STEP 1: Clean up invalid location assignments
    console.log("🧹 Cleaning up invalid location assignments...")

    // Get all staff location assignments
    const allAssignments = await prisma.staffLocation.findMany({
      include: {
        staff: true,
        location: true
      }
    })

    const validLocationIds = ['loc1', 'loc2', 'loc3', 'home', 'all']
    let cleanedCount = 0

    // Remove assignments with invalid location IDs
    for (const assignment of allAssignments) {
      if (!validLocationIds.includes(assignment.locationId)) {
        console.log(`🗑️ Removing invalid assignment: ${assignment.staff.name} -> ${assignment.locationId}`)
        await prisma.staffLocation.delete({
          where: { id: assignment.id }
        })
        cleanedCount++
      }
    }

    console.log(`✅ Cleaned up ${cleanedCount} invalid location assignments`)

    // Get all staff members
    const allStaff = await prisma.staffMember.findMany({
      include: {
        user: true
      }
    })

    console.log(`👥 Found ${allStaff.length} staff members`)

    // Define staff assignments to locations
    const staffLocationAssignments = [
      // D-ring road (loc1) - 7 staff members
      { staffName: 'Mekdes Abebe', locationId: 'loc1' },
      { staffName: 'Fatima Al-Zahra', locationId: 'loc1' },
      { staffName: 'Layla Hassan', locationId: 'loc1' },
      { staffName: 'Nour Al-Din', locationId: 'loc1' },
      { staffName: 'Amina Khoury', locationId: 'loc1' },
      { staffName: 'Yasmin Al-Rashid', locationId: 'loc1' },
      { staffName: 'Zara Mansouri', locationId: 'loc1' },
      
      // Muaither (loc2) - 7 staff members
      { staffName: 'Hala Qasemi', locationId: 'loc2' },
      { staffName: 'Rania Khalil', locationId: 'loc2' },
      { staffName: 'Dina Al-Kuwari', locationId: 'loc2' },
      { staffName: 'Mariam Farouk', locationId: 'loc2' },
      { staffName: 'Leila Badawi', locationId: 'loc2' },
      { staffName: 'Salma Al-Thani', locationId: 'loc2' },
      { staffName: 'Nadia Hamdan', locationId: 'loc2' },
      
      // Medinat Khalifa (loc3) - 6 staff members
      { staffName: 'Amal Darwish', locationId: 'loc3' },
      { staffName: 'Rana Al-Maktoum', locationId: 'loc3' },
      { staffName: 'Lina Sabbagh', locationId: 'loc3' },
      { staffName: 'Maya Al-Ansari', locationId: 'loc3' },
      { staffName: 'Huda Nassar', locationId: 'loc3' },
      { staffName: 'Sahar Al-Mahmoud', locationId: 'loc3' }
    ]

    console.log("🔗 Assigning staff to locations...")
    let assignmentCount = 0

    for (const assignment of staffLocationAssignments) {
      // Find the staff member by name
      const staffMember = allStaff.find(staff => 
        staff.name.toLowerCase().includes(assignment.staffName.toLowerCase()) ||
        assignment.staffName.toLowerCase().includes(staff.name.toLowerCase())
      )

      if (staffMember) {
        // Check if assignment already exists
        const existingAssignment = await prisma.staffLocation.findFirst({
          where: {
            staffId: staffMember.id,
            locationId: assignment.locationId
          }
        })

        if (!existingAssignment) {
          // Create new assignment
          await prisma.staffLocation.create({
            data: {
              staffId: staffMember.id,
              locationId: assignment.locationId,
              isActive: true
            }
          })
          console.log(`✅ Assigned ${staffMember.name} to ${assignment.locationId}`)
          assignmentCount++
        } else {
          // Update existing assignment to be active
          await prisma.staffLocation.update({
            where: { id: existingAssignment.id },
            data: { isActive: true }
          })
          console.log(`✅ Updated assignment: ${staffMember.name} to ${assignment.locationId}`)
          assignmentCount++
        }
      } else {
        console.warn(`⚠️ Staff member not found: ${assignment.staffName}`)
      }
    }

    // Also ensure receptionist users are properly assigned to their locations
    const receptionistUsers = [
      { email: '<EMAIL>', locationId: 'loc1' },
      { email: '<EMAIL>', locationId: 'loc2' },
      { email: '<EMAIL>', locationId: 'loc3' }
    ]

    console.log("👩‍💼 Ensuring receptionist location assignments...")
    for (const receptionist of receptionistUsers) {
      const user = await prisma.user.findFirst({
        where: { email: receptionist.email }
      })

      if (user) {
        const staffMember = await prisma.staffMember.findFirst({
          where: { userId: user.id }
        })

        if (staffMember) {
          // Ensure receptionist is assigned to their location
          const existingAssignment = await prisma.staffLocation.findFirst({
            where: {
              staffId: staffMember.id,
              locationId: receptionist.locationId
            }
          })

          if (!existingAssignment) {
            await prisma.staffLocation.create({
              data: {
                staffId: staffMember.id,
                locationId: receptionist.locationId,
                isActive: true
              }
            })
            console.log(`✅ Assigned receptionist ${user.name} to ${receptionist.locationId}`)
          }
        }
      }
    }

    // Get final counts
    const finalCounts = await Promise.all([
      prisma.staffLocation.count({ where: { locationId: 'loc1', isActive: true } }),
      prisma.staffLocation.count({ where: { locationId: 'loc2', isActive: true } }),
      prisma.staffLocation.count({ where: { locationId: 'loc3', isActive: true } })
    ])

    console.log("✅ Staff location assignments completed!")

    return NextResponse.json({
      success: true,
      message: "Staff location assignments fixed successfully",
      summary: {
        totalAssignments: assignmentCount,
        locationCounts: {
          'D-ring road (loc1)': finalCounts[0],
          'Muaither (loc2)': finalCounts[1],
          'Medinat Khalifa (loc3)': finalCounts[2]
        }
      }
    })

  } catch (error) {
    console.error("❌ Error fixing staff location assignments:", error)
    return NextResponse.json({
      success: false,
      error: "Failed to fix staff location assignments",
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 })
  }
}
