"use client"

import { useState } from "react"
import { useToast } from "@/components/ui/use-toast"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"

export default function FixStaffLocationsPage() {
  const [isFixing, setIsFixing] = useState(false)
  const [fixResults, setFixResults] = useState<any>(null)
  const { toast } = useToast()

  const runFix = async () => {
    setIsFixing(true)
    setFixResults(null)
    
    try {
      console.log("🔧 Starting staff location assignments fix...")
      
      const response = await fetch('/api/fix-staff-locations', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      })
      
      const result = await response.json()
      
      if (result.success) {
        console.log("✅ Staff location fix completed:", result)
        setFixResults(result)
        
        toast({
          title: "Fix Completed",
          description: `Fixed staff location assignments. ${result.summary.totalAssignments} assignments processed.`,
        })
        
      } else {
        console.error("❌ Fix failed:", result.error)
        toast({
          variant: "destructive",
          title: "Fix Failed",
          description: result.error || "Failed to fix staff location assignments",
        })
      }
      
    } catch (error) {
      console.error("❌ Error during fix:", error)
      toast({
        variant: "destructive",
        title: "Fix Error",
        description: "An error occurred during the fix",
      })
    } finally {
      setIsFixing(false)
    }
  }

  const testCalendar = () => {
    window.location.href = '/dashboard/appointments'
  }

  const testStaffAPI = () => {
    window.open('/api/staff?locationId=loc1', '_blank')
  }

  return (
    <div className="p-6 space-y-6">
      <h1 className="text-2xl font-bold">Fix Staff Location Assignments</h1>
      
      <div className="space-y-4">
        <div className="bg-yellow-50 p-4 rounded-lg">
          <h2 className="text-lg font-semibold mb-2">Issue Description:</h2>
          <ul className="list-disc list-inside space-y-1 text-sm">
            <li>❌ <strong>D-ring road location not showing in calendar</strong></li>
            <li>❌ <strong>Staff not appearing in D-ring road calendar view</strong></li>
            <li>❌ <strong>Location selector missing D-ring road option</strong></li>
            <li>❌ <strong>Staff location assignments may be missing or inactive</strong></li>
          </ul>
        </div>

        <div className="bg-blue-50 p-4 rounded-lg">
          <h2 className="text-lg font-semibold mb-2">What this fix will do:</h2>
          <ul className="list-disc list-inside space-y-1 text-sm">
            <li>✅ <strong>Ensure all locations exist</strong> (D-ring road, Muaither, Medinat Khalifa)</li>
            <li>✅ <strong>Assign staff to locations</strong> properly in the database</li>
            <li>✅ <strong>Activate location assignments</strong> for all staff members</li>
            <li>✅ <strong>Fix receptionist location assignments</strong></li>
            <li>✅ <strong>Verify staff distribution</strong> across all locations</li>
          </ul>
        </div>
        
        <div className="flex gap-4">
          <Button
            onClick={runFix}
            disabled={isFixing}
            className="px-6 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:opacity-50"
          >
            {isFixing ? "Fixing..." : "Fix Staff Location Assignments"}
          </Button>
          
          <Button
            onClick={testStaffAPI}
            variant="outline"
            className="px-6 py-3"
          >
            Test Staff API
          </Button>
          
          {fixResults && (
            <Button
              onClick={testCalendar}
              className="px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700"
            >
              Test Calendar
            </Button>
          )}
        </div>
      </div>

      {fixResults && (
        <div className="space-y-6">
          <h2 className="text-xl font-semibold">Fix Results</h2>
          
          <Card>
            <CardHeader>
              <CardTitle>Staff Assignment Summary</CardTitle>
              <CardDescription>Staff distribution across locations</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {Object.entries(fixResults.summary.locationCounts).map(([location, count]) => (
                  <div key={location} className="p-4 border rounded-lg">
                    <h3 className="font-semibold">{location}</h3>
                    <p className="text-2xl font-bold text-blue-600">{count as number} staff</p>
                  </div>
                ))}
              </div>
              
              <div className="mt-4 p-4 bg-green-50 rounded-lg">
                <p><strong>Total Assignments Processed:</strong> {fixResults.summary.totalAssignments}</p>
                <p className="text-sm text-green-700 mt-2">
                  All staff members should now be properly assigned to their locations and visible in the calendar.
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      <div className="space-y-4">
        <h2 className="text-xl font-semibold">Expected Staff Distribution</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card>
            <CardHeader>
              <CardTitle>D-ring road (loc1)</CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="text-sm space-y-1">
                <li>• Mekdes Abebe</li>
                <li>• Fatima Al-Zahra</li>
                <li>• Layla Hassan</li>
                <li>• Nour Al-Din</li>
                <li>• Amina Khoury</li>
                <li>• Yasmin Al-Rashid</li>
                <li>• Zara Mansouri</li>
                <li>• Sarah Al-Rashid (Receptionist)</li>
              </ul>
              <p className="text-sm font-medium mt-2">Expected: 7-8 staff</p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle>Muaither (loc2)</CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="text-sm space-y-1">
                <li>• Hala Qasemi</li>
                <li>• Rania Khalil</li>
                <li>• Dina Al-Kuwari</li>
                <li>• Mariam Farouk</li>
                <li>• Leila Badawi</li>
                <li>• Salma Al-Thani</li>
                <li>• Nadia Hamdan</li>
                <li>• Fatima Al-Kuwari (Receptionist)</li>
              </ul>
              <p className="text-sm font-medium mt-2">Expected: 7-8 staff</p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle>Medinat Khalifa (loc3)</CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="text-sm space-y-1">
                <li>• Amal Darwish</li>
                <li>• Rana Al-Maktoum</li>
                <li>• Lina Sabbagh</li>
                <li>• Maya Al-Ansari</li>
                <li>• Huda Nassar</li>
                <li>• Sahar Al-Mahmoud</li>
                <li>• Aisha Al-Thani (Receptionist)</li>
              </ul>
              <p className="text-sm font-medium mt-2">Expected: 6-7 staff</p>
            </CardContent>
          </Card>
        </div>
      </div>

      <div className="space-y-4">
        <h2 className="text-xl font-semibold">Testing Instructions</h2>
        <div className="bg-blue-50 p-4 rounded-lg">
          <ol className="list-decimal list-inside space-y-2 text-sm">
            <li><strong>Run the fix</strong> by clicking "Fix Staff Location Assignments"</li>
            <li><strong>Test Staff API</strong> to verify staff are assigned to D-ring road</li>
            <li><strong>Test Calendar</strong> to see if D-ring road appears in location selector</li>
            <li><strong>Check staff list</strong> in calendar view for D-ring road</li>
            <li><strong>Test receptionist login</strong> to verify location-specific access</li>
          </ol>
        </div>
      </div>

      <div className="space-y-4">
        <h2 className="text-xl font-semibold">Verification Links</h2>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <Button 
            variant="outline" 
            onClick={() => window.open('/api/staff?locationId=loc1', '_blank')}
          >
            D-ring Staff API
          </Button>
          <Button 
            variant="outline" 
            onClick={() => window.open('/api/staff?locationId=loc2', '_blank')}
          >
            Muaither Staff API
          </Button>
          <Button 
            variant="outline" 
            onClick={() => window.open('/api/staff?locationId=loc3', '_blank')}
          >
            Medinat Staff API
          </Button>
          <Button 
            variant="outline" 
            onClick={() => window.open('/api/locations', '_blank')}
          >
            Locations API
          </Button>
        </div>
      </div>
    </div>
  )
}
