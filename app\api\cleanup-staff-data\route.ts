import { NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"

/**
 * POST /api/cleanup-staff-data
 * 
 * Cleans up staff data by removing invalid location IDs and ensuring proper assignments
 */
export async function POST() {
  try {
    console.log("🧹 Starting staff data cleanup...")

    const validLocationIds = ['loc1', 'loc2', 'loc3', 'home', 'all']
    let cleanupResults = {
      invalidAssignmentsRemoved: 0,
      staffUpdated: 0,
      errors: [] as string[]
    }

    // STEP 1: Clean up database staff location assignments
    console.log("🔍 Checking database staff location assignments...")
    
    const allAssignments = await prisma.staffLocation.findMany({
      include: {
        staff: true,
        location: true
      }
    })

    console.log(`Found ${allAssignments.length} staff location assignments`)

    // Remove assignments with invalid location IDs
    for (const assignment of allAssignments) {
      if (!validLocationIds.includes(assignment.locationId)) {
        try {
          console.log(`🗑️ Removing invalid assignment: ${assignment.staff.name} -> ${assignment.locationId}`)
          await prisma.staffLocation.delete({
            where: { id: assignment.id }
          })
          cleanupResults.invalidAssignmentsRemoved++
        } catch (error) {
          const errorMsg = `Failed to remove assignment for ${assignment.staff.name}: ${error}`
          console.error(errorMsg)
          cleanupResults.errors.push(errorMsg)
        }
      }
    }

    // STEP 2: Check for staff with no location assignments and assign them to loc1 (D-ring road) as default
    console.log("🔍 Checking for staff without location assignments...")
    
    const staffWithoutLocations = await prisma.staffMember.findMany({
      where: {
        status: 'ACTIVE',
        locations: {
          none: {}
        }
      }
    })

    console.log(`Found ${staffWithoutLocations.length} staff members without location assignments`)

    for (const staff of staffWithoutLocations) {
      try {
        // Assign to D-ring road (loc1) as default
        await prisma.staffLocation.create({
          data: {
            staffId: staff.id,
            locationId: 'loc1',
            isActive: true
          }
        })
        console.log(`✅ Assigned ${staff.name} to default location (loc1)`)
        cleanupResults.staffUpdated++
      } catch (error) {
        const errorMsg = `Failed to assign default location to ${staff.name}: ${error}`
        console.error(errorMsg)
        cleanupResults.errors.push(errorMsg)
      }
    }

    // STEP 3: Get final statistics
    const finalStats = await Promise.all([
      prisma.staffLocation.count({ where: { locationId: 'loc1', isActive: true } }),
      prisma.staffLocation.count({ where: { locationId: 'loc2', isActive: true } }),
      prisma.staffLocation.count({ where: { locationId: 'loc3', isActive: true } }),
      prisma.staffLocation.count({ where: { locationId: 'home', isActive: true } })
    ])

    console.log("✅ Staff data cleanup completed!")

    return NextResponse.json({
      success: true,
      message: "Staff data cleanup completed successfully",
      results: cleanupResults,
      finalStats: {
        'D-ring road (loc1)': finalStats[0],
        'Muaither (loc2)': finalStats[1],
        'Medinat Khalifa (loc3)': finalStats[2],
        'Home Service': finalStats[3]
      }
    })

  } catch (error) {
    console.error("❌ Error during staff data cleanup:", error)
    return NextResponse.json({
      success: false,
      error: "Failed to cleanup staff data",
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 })
  }
}
