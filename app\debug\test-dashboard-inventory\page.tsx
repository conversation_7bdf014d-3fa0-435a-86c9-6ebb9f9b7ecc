"use client"

import { useState, useEffect } from "react"
import { useAuth } from "@/lib/auth-provider"

export default function TestDashboardInventoryPage() {
  const [inventoryData, setInventoryData] = useState({
    value: 0,
    lowStockItems: 0,
    outOfStockItems: 0,
    turnoverRate: 0
  })
  const [debugInfo, setDebugInfo] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const { currentLocation } = useAuth()

  const testInventoryCalculation = async () => {
    setIsLoading(true)
    setDebugInfo([])
    
    const addDebug = (message: string, data?: any) => {
      console.log(message, data)
      setDebugInfo(prev => [...prev, { message, data, timestamp: new Date().toISOString() }])
    }

    try {
      addDebug('🔄 Starting inventory calculation test...')
      addDebug('📍 Current location:', currentLocation)

      let totalValue = 0
      let lowStockItems = 0
      let outOfStockItems = 0
      let totalItems = 0

      // Get all active locations to calculate inventory across all locations or specific location
      const locationsToCheck = currentLocation === 'all' 
        ? ['loc1', 'loc2', 'loc3', 'home', 'online'] // All location IDs
        : [currentLocation]

      addDebug('📍 Locations to check:', locationsToCheck)

      // Fetch inventory data for each location
      for (const locationId of locationsToCheck) {
        try {
          addDebug(`🔄 Fetching inventory for location: ${locationId}`)
          
          const response = await fetch(`/api/inventory?locationId=${locationId}`)
          addDebug(`📡 Response status for ${locationId}:`, response.status)
          
          if (response.ok) {
            const data = await response.json()
            const inventory = data.inventory || []
            
            addDebug(`📦 Location ${locationId} inventory:`, {
              count: inventory.length,
              sample: inventory.slice(0, 3)
            })

            inventory.forEach((item: any, index: number) => {
              const stock = item.stock || 0
              const cost = item.product?.cost || item.cost || 0
              
              addDebug(`📊 Item ${index + 1} - ${item.product?.name || item.name}:`, {
                stock,
                cost,
                costType: typeof cost
              })
              
              if (stock > 0 && cost > 0) {
                const itemValue = stock * parseFloat(cost.toString())
                totalValue += itemValue
                totalItems += stock
                
                // Check for low stock (less than 5 units)
                if (stock < 5) {
                  lowStockItems++
                }
                
                addDebug(`💰 Value calculation:`, {
                  product: item.product?.name || item.name,
                  stock,
                  cost,
                  itemValue,
                  runningTotal: totalValue
                })
              } else if (stock === 0) {
                outOfStockItems++
                addDebug(`❌ Out of stock:`, item.product?.name || item.name)
              } else {
                addDebug(`⚠️ No value (missing stock or cost):`, {
                  product: item.product?.name || item.name,
                  stock,
                  cost
                })
              }
            })
          } else {
            const errorText = await response.text()
            addDebug(`❌ Failed to fetch inventory for location ${locationId}:`, {
              status: response.status,
              error: errorText
            })
          }
        } catch (error) {
          addDebug(`❌ Error fetching inventory for location ${locationId}:`, error)
        }
      }

      // Calculate turnover rate (mock calculation)
      const turnoverRate = totalValue > 0 ? Math.round((totalValue * 0.1) / totalValue * 12 * 10) / 10 : 0

      const finalResult = {
        value: totalValue,
        lowStockItems,
        outOfStockItems,
        turnoverRate
      }

      addDebug('🎯 Final inventory calculation result:', finalResult)

      setInventoryData(finalResult)

    } catch (error) {
      addDebug('❌ Error in inventory calculation:', error)
    } finally {
      setIsLoading(false)
    }
  }

  // Test the same calculation that the dashboard should be doing
  useEffect(() => {
    testInventoryCalculation()
  }, [currentLocation])

  const copyDashboardCode = () => {
    const code = `
// Add this useEffect to your dashboard stats-cards.tsx component:

useEffect(() => {
  console.log('📊 STATS CARDS: Calculating inventory value and metrics')

  const calculateInventoryMetrics = async () => {
    try {
      let totalValue = 0
      let lowStockItems = 0
      let outOfStockItems = 0
      let totalItems = 0

      const locationsToCheck = currentLocation === 'all' 
        ? ['loc1', 'loc2', 'loc3', 'home', 'online']
        : [currentLocation]

      console.log('📊 INVENTORY: Checking locations:', locationsToCheck)

      for (const locationId of locationsToCheck) {
        try {
          const response = await fetch(\`/api/inventory?locationId=\${locationId}\`)
          if (response.ok) {
            const data = await response.json()
            const inventory = data.inventory || []
            
            console.log(\`📊 INVENTORY: Location \${locationId} has \${inventory.length} products\`)

            inventory.forEach((item: any) => {
              const stock = item.stock || 0
              const cost = item.product?.cost || item.cost || 0
              
              if (stock > 0 && cost > 0) {
                const itemValue = stock * parseFloat(cost.toString())
                totalValue += itemValue
                totalItems += stock
                
                if (stock < 5) {
                  lowStockItems++
                }
                
                console.log(\`📊 INVENTORY: \${item.product?.name || item.name}: \${stock} units × \${cost} = \${itemValue}\`)
              } else if (stock === 0) {
                outOfStockItems++
              }
            })
          }
        } catch (error) {
          console.error(\`📊 INVENTORY: Error fetching inventory for location \${locationId}:\`, error)
        }
      }

      const turnoverRate = totalValue > 0 ? Math.round((totalValue * 0.1) / totalValue * 12 * 10) / 10 : 0

      setInventoryData({
        value: totalValue,
        lowStockItems,
        outOfStockItems,
        turnoverRate
      })

    } catch (error) {
      console.error('📊 STATS CARDS: Error calculating inventory metrics:', error)
      setInventoryData({
        value: 0,
        lowStockItems: 0,
        outOfStockItems: 0,
        turnoverRate: 0
      })
    }
  }

  calculateInventoryMetrics()
}, [currentLocation])
`
    navigator.clipboard.writeText(code)
    alert('Dashboard code copied to clipboard!')
  }

  return (
    <div className="p-6 space-y-6">
      <h1 className="text-2xl font-bold">Test Dashboard Inventory Calculation</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Current Results */}
        <div className="space-y-4">
          <h2 className="text-xl font-semibold">Calculation Results</h2>
          <div className="bg-gray-100 p-4 rounded">
            <p><strong>Current Location:</strong> {currentLocation}</p>
            <p><strong>Total Value:</strong> QAR {inventoryData.value.toFixed(2)}</p>
            <p><strong>Low Stock Items:</strong> {inventoryData.lowStockItems}</p>
            <p><strong>Out of Stock Items:</strong> {inventoryData.outOfStockItems}</p>
            <p><strong>Turnover Rate:</strong> {inventoryData.turnoverRate}x/year</p>
          </div>
          
          <div className="flex gap-2">
            <button
              onClick={testInventoryCalculation}
              disabled={isLoading}
              className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
            >
              {isLoading ? "Testing..." : "Re-test Calculation"}
            </button>
            
            <button
              onClick={() => window.location.href = '/dashboard'}
              className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
            >
              Go to Dashboard
            </button>
            
            <button
              onClick={copyDashboardCode}
              className="px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700"
            >
              Copy Dashboard Code
            </button>
          </div>
        </div>

        {/* Debug Information */}
        <div className="space-y-4">
          <h2 className="text-xl font-semibold">Debug Information</h2>
          <div className="bg-gray-100 p-4 rounded max-h-96 overflow-y-auto">
            {debugInfo.length === 0 ? (
              <p>No debug information yet. Click "Re-test Calculation" to see details.</p>
            ) : (
              debugInfo.map((info, index) => (
                <div key={index} className="mb-2 text-sm">
                  <div className="font-mono text-xs text-gray-500">
                    {new Date(info.timestamp).toLocaleTimeString()}
                  </div>
                  <div className="font-medium">{info.message}</div>
                  {info.data && (
                    <pre className="text-xs bg-white p-1 rounded mt-1 overflow-x-auto">
                      {JSON.stringify(info.data, null, 2)}
                    </pre>
                  )}
                </div>
              ))
            )}
          </div>
        </div>
      </div>

      {/* Issue Analysis */}
      <div className="space-y-4">
        <h2 className="text-xl font-semibold">Issue Analysis</h2>
        <div className="bg-yellow-50 p-4 rounded">
          <h3 className="font-semibold mb-2">Possible reasons why dashboard shows QAR 0.00:</h3>
          <ul className="list-disc list-inside space-y-1 text-sm">
            <li>The dashboard component isn't calling the inventory calculation useEffect</li>
            <li>The inventory API endpoints are returning empty data</li>
            <li>Products don't have cost data in the database</li>
            <li>Product locations don't have stock quantities</li>
            <li>The calculation logic has a bug</li>
            <li>The dashboard is using cached/stale data</li>
          </ul>
          
          <h3 className="font-semibold mt-4 mb-2">This test will help identify:</h3>
          <ul className="list-disc list-inside space-y-1 text-sm">
            <li>Whether the calculation logic works correctly</li>
            <li>What data is actually returned from the APIs</li>
            <li>Where the calculation might be failing</li>
            <li>If the issue is in the dashboard component or the data</li>
          </ul>
        </div>
      </div>
    </div>
  )
}
