"use client"

import { useAuth } from "@/lib/auth-provider"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { RoleGuard, NoReceptionistGuard, ReceptionistOnlyGuard } from "@/components/auth/role-guard"
import { ReceptionistNav, ReceptionistRestrictedNotice } from "@/components/navigation/receptionist-nav"

export default function TestReceptionistAccessPage() {
  const { user, currentLocation, canAccessLocation, getUserAccessibleLocations } = useAuth()

  if (!user) {
    return <div className="p-6">Please log in to test access control</div>
  }

  const isReceptionist = user.role === 'RECEPTIONIST' || user.role === 'receptionist'
  const accessibleLocations = getUserAccessibleLocations()

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Show receptionist navigation if user is a receptionist */}
      {isReceptionist && <ReceptionistNav />}
      
      <div className="p-6 space-y-6">
        <h1 className="text-2xl font-bold">Receptionist Access Control Test</h1>
        
        {/* Current User Info */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              Current User Information
              <Badge variant={isReceptionist ? "default" : "secondary"}>
                {user.role}
              </Badge>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <p><strong>Name:</strong> {user.name}</p>
            <p><strong>Email:</strong> {user.email}</p>
            <p><strong>Role:</strong> {user.role}</p>
            <p><strong>Current Location:</strong> {currentLocation}</p>
            <p><strong>User Locations:</strong> {user.locations?.join(', ') || 'None'}</p>
            <p><strong>Accessible Locations:</strong> {accessibleLocations.join(', ')}</p>
            <p><strong>Is Receptionist:</strong> {isReceptionist ? 'Yes' : 'No'}</p>
          </CardContent>
        </Card>

        {/* Location Access Test */}
        <Card>
          <CardHeader>
            <CardTitle>Location Access Test</CardTitle>
            <CardDescription>Testing which locations this user can access</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {['all', 'loc1', 'loc2', 'loc3', 'online'].map((locationId) => {
                const canAccess = canAccessLocation(locationId)
                const locationName = {
                  'all': 'All Locations',
                  'loc1': 'D-ring road',
                  'loc2': 'Muaither', 
                  'loc3': 'Medinat Khalifa',
                  'online': 'Online'
                }[locationId] || locationId

                return (
                  <div key={locationId} className={`p-3 rounded-lg border ${canAccess ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'}`}>
                    <div className="font-medium">{locationName}</div>
                    <div className="text-sm">
                      <Badge variant={canAccess ? "default" : "destructive"}>
                        {canAccess ? 'Accessible' : 'Restricted'}
                      </Badge>
                    </div>
                  </div>
                )
              })}
            </div>
          </CardContent>
        </Card>

        {/* Role-Based Component Access Test */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Admin Only Content */}
          <Card>
            <CardHeader>
              <CardTitle>Admin Only Content</CardTitle>
              <CardDescription>This should only be visible to admins</CardDescription>
            </CardHeader>
            <CardContent>
              <NoReceptionistGuard fallback={
                <div className="text-red-600 text-sm">
                  ❌ Hidden from receptionists
                </div>
              }>
                <div className="text-green-600 text-sm">
                  ✅ Admin content visible
                </div>
                <p className="text-sm mt-2">
                  This content includes financial reports, system settings, user management, etc.
                </p>
              </NoReceptionistGuard>
            </CardContent>
          </Card>

          {/* Receptionist Only Content */}
          <Card>
            <CardHeader>
              <CardTitle>Receptionist Only Content</CardTitle>
              <CardDescription>This should only be visible to receptionists</CardDescription>
            </CardHeader>
            <CardContent>
              <ReceptionistOnlyGuard fallback={
                <div className="text-red-600 text-sm">
                  ❌ Hidden from non-receptionists
                </div>
              }>
                <div className="text-green-600 text-sm">
                  ✅ Receptionist content visible
                </div>
                <p className="text-sm mt-2">
                  This content includes receptionist-specific tools and information.
                </p>
              </ReceptionistOnlyGuard>
            </CardContent>
          </Card>
        </div>

        {/* Navigation Test */}
        {isReceptionist && (
          <Card>
            <CardHeader>
              <CardTitle>Receptionist Navigation</CardTitle>
              <CardDescription>Special navigation bar for receptionists</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-600 mb-4">
                The navigation bar above shows only the pages that receptionists are allowed to access.
              </p>
              <div className="space-y-2">
                <h4 className="font-medium">Allowed Pages:</h4>
                <ul className="text-sm space-y-1 text-gray-600">
                  <li>✅ Appointments (view and manage for their location)</li>
                  <li>✅ Clients (manage client information)</li>
                  <li>✅ New Appointment (book appointments)</li>
                  <li>✅ Schedule (view staff schedules)</li>
                  <li>✅ Services (view services and pricing)</li>
                  <li>✅ Reports (basic reports for their location)</li>
                </ul>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Restricted Page Example */}
        {isReceptionist && (
          <Card>
            <CardHeader>
              <CardTitle>Restricted Page Example</CardTitle>
              <CardDescription>Example of what receptionists see when accessing restricted pages</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="border rounded-lg p-4 bg-gray-50">
                <ReceptionistRestrictedNotice 
                  pageName="Financial Reports"
                  reason="Financial data is restricted to management and admin users only."
                />
              </div>
            </CardContent>
          </Card>
        )}

        {/* Test Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Test Actions</CardTitle>
            <CardDescription>Actions to test the access control system</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Button 
                onClick={() => window.location.href = '/dashboard/appointments'}
                className="w-full"
              >
                Test Appointments Page
              </Button>
              <Button 
                onClick={() => window.location.href = '/dashboard/clients'}
                variant="outline"
                className="w-full"
              >
                Test Clients Page
              </Button>
              <Button 
                onClick={() => window.location.href = '/dashboard/reports'}
                variant="outline"
                className="w-full"
              >
                Test Reports Page
              </Button>
            </div>
            
            <div className="pt-4 border-t">
              <h4 className="font-medium mb-2">Expected Behavior for Receptionists:</h4>
              <ul className="text-sm space-y-1 text-gray-600">
                <li>• Should only see appointments for their assigned location</li>
                <li>• Cannot switch to "All Locations" view</li>
                <li>• Cannot access financial or admin pages</li>
                <li>• Can manage clients and book appointments</li>
                <li>• Can view basic reports filtered to their location</li>
              </ul>
            </div>
          </CardContent>
        </Card>

        {/* Login Test Links */}
        <Card>
          <CardHeader>
            <CardTitle>Test Different Users</CardTitle>
            <CardDescription>Quick links to test with different user types</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="p-4 border rounded-lg">
                <h4 className="font-medium">D-ring road Receptionist</h4>
                <p className="text-sm text-gray-600 mb-2"><EMAIL></p>
                <Button 
                  size="sm" 
                  variant="outline"
                  onClick={() => window.open('/auth/signin?email=<EMAIL>', '_blank')}
                >
                  Test Login
                </Button>
              </div>
              <div className="p-4 border rounded-lg">
                <h4 className="font-medium">Muaither Receptionist</h4>
                <p className="text-sm text-gray-600 mb-2"><EMAIL></p>
                <Button 
                  size="sm" 
                  variant="outline"
                  onClick={() => window.open('/auth/signin?email=<EMAIL>', '_blank')}
                >
                  Test Login
                </Button>
              </div>
              <div className="p-4 border rounded-lg">
                <h4 className="font-medium">Medinat Khalifa Receptionist</h4>
                <p className="text-sm text-gray-600 mb-2"><EMAIL></p>
                <Button 
                  size="sm" 
                  variant="outline"
                  onClick={() => window.open('/auth/signin?email=<EMAIL>', '_blank')}
                >
                  Test Login
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
