"use client"

import Link from "next/link"
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"

const debugTools = [
  {
    title: "Staff Data Cleanup",
    description: "Clean up invalid location assignments and ensure proper staff-location relationships",
    href: "/debug/cleanup-staff-data",
    category: "Staff Management",
    priority: "high"
  },
  {
    title: "Fix Staff Location Assignments",
    description: "Assign staff members to their proper locations and fix location-based access",
    href: "/debug/fix-staff-locations",
    category: "Staff Management",
    priority: "high"
  },
  {
    title: "Test Staff Assignments",
    description: "View and verify current staff location assignments",
    href: "/debug/test-staff-assignments",
    category: "Staff Management",
    priority: "medium"
  },
  {
    title: "View Staff Data",
    description: "View and inspect current staff data and location assignments",
    href: "/debug/view-staff-data",
    category: "Staff Management",
    priority: "medium"
  },
  {
    title: "Fix D-Ring Access",
    description: "Fix D-Ring Road location access and staff assignments",
    href: "/debug/fix-dring-access",
    category: "Location Management",
    priority: "high"
  },
  {
    title: "Setup D-Ring Complete",
    description: "Complete D-Ring Road location setup with staff and services",
    href: "/debug/setup-dring-complete",
    category: "Location Management",
    priority: "medium"
  },
  {
    title: "Location Access Debug",
    description: "Debug location-based access control and permissions",
    href: "/debug/location-access-debug",
    category: "Location Management",
    priority: "medium"
  },
  {
    title: "Calendar Location Debug",
    description: "Debug calendar location filtering and staff visibility",
    href: "/debug/calendar-location-debug",
    category: "Calendar",
    priority: "medium"
  },
  {
    title: "Location Selector Test",
    description: "Test location selector component functionality",
    href: "/debug/location-selector-test",
    category: "Calendar",
    priority: "low"
  },
  {
    title: "Setup Receptionist Users",
    description: "Create and configure receptionist user accounts for each location",
    href: "/debug/setup-receptionist-users",
    category: "User Management",
    priority: "medium"
  },
  {
    title: "Test Receptionist Access",
    description: "Test receptionist location-restricted access",
    href: "/debug/test-receptionist-access",
    category: "User Management",
    priority: "medium"
  },
  {
    title: "Revenue Integration",
    description: "Test and debug revenue calculation integration",
    href: "/debug/revenue-integration",
    category: "Financial",
    priority: "low"
  },
  {
    title: "Inventory Value Fix",
    description: "Fix inventory value calculations and stock adjustments",
    href: "/debug/fix-inventory-value",
    category: "Inventory",
    priority: "low"
  }
]

const categories = Array.from(new Set(debugTools.map(tool => tool.category)))

const getPriorityColor = (priority: string) => {
  switch (priority) {
    case "high": return "border-red-200 bg-red-50"
    case "medium": return "border-yellow-200 bg-yellow-50"
    case "low": return "border-green-200 bg-green-50"
    default: return "border-gray-200 bg-gray-50"
  }
}

const getPriorityBadge = (priority: string) => {
  switch (priority) {
    case "high": return "bg-red-100 text-red-800"
    case "medium": return "bg-yellow-100 text-yellow-800"
    case "low": return "bg-green-100 text-green-800"
    default: return "bg-gray-100 text-gray-800"
  }
}

export default function DebugPage() {
  return (
    <div className="container mx-auto p-6 space-y-6">
      <div>
        <h1 className="text-3xl font-bold">VanityERP Debug Tools</h1>
        <p className="text-muted-foreground">
          Development and debugging utilities for VanityERP system
        </p>
      </div>

      <div className="bg-yellow-50 p-4 rounded-lg">
        <h2 className="text-lg font-semibold mb-2">⚠️ Important Notice</h2>
        <p className="text-sm">
          These tools are for development and debugging purposes only. Use with caution in production environments.
          Always backup your data before running any fix or cleanup operations.
        </p>
      </div>

      {categories.map(category => (
        <div key={category} className="space-y-4">
          <h2 className="text-2xl font-semibold">{category}</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {debugTools
              .filter(tool => tool.category === category)
              .sort((a, b) => {
                const priorityOrder = { high: 0, medium: 1, low: 2 }
                return priorityOrder[a.priority as keyof typeof priorityOrder] - priorityOrder[b.priority as keyof typeof priorityOrder]
              })
              .map(tool => (
                <Link key={tool.href} href={tool.href}>
                  <Card className={`h-full hover:shadow-md transition-shadow cursor-pointer ${getPriorityColor(tool.priority)}`}>
                    <CardHeader>
                      <div className="flex items-center justify-between">
                        <CardTitle className="text-lg">{tool.title}</CardTitle>
                        <span className={`px-2 py-1 text-xs rounded-full ${getPriorityBadge(tool.priority)}`}>
                          {tool.priority}
                        </span>
                      </div>
                      <CardDescription>{tool.description}</CardDescription>
                    </CardHeader>
                  </Card>
                </Link>
              ))}
          </div>
        </div>
      ))}

      <div className="space-y-4">
        <h2 className="text-xl font-semibold">Recommended Workflow</h2>
        <div className="bg-blue-50 p-4 rounded-lg">
          <ol className="list-decimal list-inside space-y-2 text-sm">
            <li><strong>Staff Data Cleanup</strong> - Remove invalid location assignments first</li>
            <li><strong>Fix Staff Location Assignments</strong> - Assign staff to proper locations</li>
            <li><strong>Fix D-Ring Access</strong> - Ensure D-Ring Road location is properly configured</li>
            <li><strong>Test Calendar</strong> - Verify staff appear in correct location views</li>
            <li><strong>Test Receptionist Access</strong> - Verify location-restricted access works</li>
          </ol>
        </div>
      </div>
    </div>
  )
}
