<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Diagnose Location Access Issue</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .section {
            border: 1px solid #ddd;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
        }
        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
    </style>
</head>
<body>
    <h1>🔍 Diagnose Location Access Issue</h1>
    <p>This page helps diagnose why location-based access control is not working properly.</p>

    <div class="section error">
        <h3>❌ Current Issues</h3>
        <ul>
            <li>Staff member sees all location buttons instead of only D-Ring Road</li>
            <li>Calendar is empty - not showing D-Ring Road staff</li>
            <li>Services page is not displaying services</li>
        </ul>
    </div>

    <div class="grid">
        <div class="section">
            <h3>🔍 Current User State</h3>
            <button onclick="checkUserState()">Check User State</button>
            <div id="userState"></div>
        </div>

        <div class="section">
            <h3>🔍 Location Data</h3>
            <button onclick="checkLocationData()">Check Location Data</button>
            <div id="locationData"></div>
        </div>
    </div>

    <div class="grid">
        <div class="section">
            <h3>🔍 Staff Data</h3>
            <button onclick="checkStaffData()">Check Staff Data</button>
            <div id="staffData"></div>
        </div>

        <div class="section">
            <h3>🔍 Services Data</h3>
            <button onclick="checkServicesData()">Check Services Data</button>
            <div id="servicesData"></div>
        </div>
    </div>

    <div class="section">
        <h3>🔧 Fix Actions</h3>
        <button onclick="fixUserData()">Fix User Data</button>
        <button onclick="clearAllData()">Clear All Data</button>
        <button onclick="setupCorrectStaffUser()">Setup Correct Staff User</button>
        <div id="fixResults"></div>
    </div>

    <div class="section">
        <h3>✅ Test Pages</h3>
        <button onclick="window.open('/dashboard/appointments', '_blank')">Test Appointments Page</button>
        <button onclick="window.open('/dashboard/services', '_blank')">Test Services Page</button>
        <button onclick="window.open('/login', '_blank')">Go to Login</button>
    </div>

    <script>
        function checkUserState() {
            const userState = document.getElementById('userState');
            
            try {
                const storedUser = localStorage.getItem('vanity_user');
                const currentLocation = localStorage.getItem('vanity_location');
                
                let results = '<h4>User State:</h4>';
                
                if (storedUser) {
                    const user = JSON.parse(storedUser);
                    results += `<pre>${JSON.stringify(user, null, 2)}</pre>`;
                    results += `<p><strong>Current Location:</strong> ${currentLocation || 'Not set'}</p>`;
                    
                    // Analyze issues
                    results += '<h4>Analysis:</h4><ul>';
                    
                    if (user.role === 'staff' && user.locations.includes('loc1') && currentLocation === 'loc1') {
                        results += '<li class="success">✅ User configuration looks correct</li>';
                    } else {
                        results += '<li class="error">❌ User configuration has issues</li>';
                        if (user.role !== 'staff') results += `<li>- Role is ${user.role}, should be staff</li>`;
                        if (!user.locations.includes('loc1')) results += `<li>- Locations: ${user.locations.join(', ')}, should include loc1</li>`;
                        if (currentLocation !== 'loc1') results += `<li>- Current location: ${currentLocation}, should be loc1</li>`;
                    }
                    
                    results += '</ul>';
                } else {
                    results += '<p class="error">❌ No user logged in</p>';
                }
                
                userState.innerHTML = results;
            } catch (error) {
                userState.innerHTML = `<p class="error">Error: ${error.message}</p>`;
            }
        }

        function checkLocationData() {
            const locationData = document.getElementById('locationData');
            
            try {
                const locations = localStorage.getItem('vanity_locations');
                
                let results = '<h4>Location Data:</h4>';
                
                if (locations) {
                    const locationArray = JSON.parse(locations);
                    results += `<p><strong>Total Locations:</strong> ${locationArray.length}</p>`;
                    results += '<h5>Locations:</h5><ul>';
                    locationArray.forEach(loc => {
                        results += `<li><strong>${loc.id}</strong>: ${loc.name} (${loc.status})</li>`;
                    });
                    results += '</ul>';
                } else {
                    results += '<p class="warning">⚠️ No location data found in localStorage</p>';
                }
                
                locationData.innerHTML = results;
            } catch (error) {
                locationData.innerHTML = `<p class="error">Error: ${error.message}</p>`;
            }
        }

        function checkStaffData() {
            const staffData = document.getElementById('staffData');
            
            try {
                // Check localStorage staff data
                const staff = localStorage.getItem('vanity_staff');
                
                let results = '<h4>Staff Data:</h4>';
                
                if (staff) {
                    const staffArray = JSON.parse(staff);
                    results += `<p><strong>Total Staff:</strong> ${staffArray.length}</p>`;
                    
                    // Group by location
                    const staffByLocation = {};
                    staffArray.forEach(s => {
                        s.locations.forEach(loc => {
                            if (!staffByLocation[loc]) staffByLocation[loc] = [];
                            staffByLocation[loc].push(s.name);
                        });
                    });
                    
                    results += '<h5>Staff by Location:</h5>';
                    Object.keys(staffByLocation).forEach(loc => {
                        results += `<p><strong>${loc}</strong>: ${staffByLocation[loc].join(', ')}</p>`;
                    });
                } else {
                    results += '<p class="warning">⚠️ No staff data found in localStorage</p>';
                }
                
                staffData.innerHTML = results;
            } catch (error) {
                staffData.innerHTML = `<p class="error">Error: ${error.message}</p>`;
            }
        }

        function checkServicesData() {
            const servicesData = document.getElementById('servicesData');
            
            try {
                const services = localStorage.getItem('vanity_services');
                
                let results = '<h4>Services Data:</h4>';
                
                if (services) {
                    const servicesArray = JSON.parse(services);
                    results += `<p><strong>Total Services:</strong> ${servicesArray.length}</p>`;
                    
                    // Group by location
                    const servicesByLocation = {};
                    servicesArray.forEach(s => {
                        s.locations.forEach(loc => {
                            if (!servicesByLocation[loc]) servicesByLocation[loc] = [];
                            servicesByLocation[loc].push(s.name);
                        });
                    });
                    
                    results += '<h5>Services by Location:</h5>';
                    Object.keys(servicesByLocation).forEach(loc => {
                        results += `<p><strong>${loc}</strong>: ${servicesByLocation[loc].length} services</p>`;
                    });
                } else {
                    results += '<p class="warning">⚠️ No services data found in localStorage</p>';
                }
                
                servicesData.innerHTML = results;
            } catch (error) {
                servicesData.innerHTML = `<p class="error">Error: ${error.message}</p>`;
            }
        }

        function fixUserData() {
            const fixResults = document.getElementById('fixResults');
            
            try {
                // Create correct staff user
                const correctUser = {
                    id: "1",
                    name: "Demo Staff",
                    email: "<EMAIL>",
                    role: "staff",
                    locations: ["loc1"] // Only D-Ring Road
                };
                
                localStorage.setItem('vanity_user', JSON.stringify(correctUser));
                localStorage.setItem('vanity_location', 'loc1');
                
                fixResults.innerHTML = '<p class="success">✅ Fixed user data - refresh the page to see changes</p>';
            } catch (error) {
                fixResults.innerHTML = `<p class="error">Error: ${error.message}</p>`;
            }
        }

        function clearAllData() {
            const fixResults = document.getElementById('fixResults');
            
            try {
                localStorage.clear();
                fixResults.innerHTML = '<p class="success">✅ Cleared all localStorage data - please login again</p>';
            } catch (error) {
                fixResults.innerHTML = `<p class="error">Error: ${error.message}</p>`;
            }
        }

        function setupCorrectStaffUser() {
            const fixResults = document.getElementById('fixResults');
            
            try {
                // Clear existing data
                localStorage.clear();
                
                // Setup correct staff user
                const staffUser = {
                    id: "1",
                    name: "Demo Staff",
                    email: "<EMAIL>",
                    role: "staff",
                    locations: ["loc1"]
                };
                
                localStorage.setItem('vanity_user', JSON.stringify(staffUser));
                localStorage.setItem('vanity_location', 'loc1');
                
                fixResults.innerHTML = `
                    <div class="success">
                        <h4>✅ Setup Complete</h4>
                        <p>Staff user configured for D-Ring Road (loc1)</p>
                        <p><strong>Next:</strong> Refresh appointments page to test</p>
                    </div>
                `;
            } catch (error) {
                fixResults.innerHTML = `<p class="error">Error: ${error.message}</p>`;
            }
        }

        // Auto-check on page load
        window.onload = function() {
            checkUserState();
            checkLocationData();
        };
    </script>
</body>
</html>
