<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Setup D-Ring Road Data</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .section {
            border: 1px solid #ddd;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
        }
        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
    </style>
</head>
<body>
    <h1>🏗️ Setup D-Ring Road Data</h1>
    <p>This page sets up complete data for D-Ring Road location testing.</p>

    <div class="section info">
        <h3>📋 What This Will Setup</h3>
        <ul>
            <li>✅ Staff user restricted to D-Ring Road (loc1)</li>
            <li>✅ Staff member: Mekdes Abebe assigned to D-Ring Road</li>
            <li>✅ Sample appointments for D-Ring Road location</li>
            <li>✅ Services available at D-Ring Road</li>
            <li>✅ Location data with proper IDs</li>
        </ul>
    </div>

    <div class="section">
        <h3>🔧 Setup Actions</h3>
        <button onclick="setupCompleteData()">Setup Complete D-Ring Road Data</button>
        <button onclick="setupUserOnly()">Setup User Only</button>
        <button onclick="setupAppointmentsOnly()">Setup Appointments Only</button>
        <button onclick="clearAllData()">Clear All Data</button>
        <div id="setupResults"></div>
    </div>

    <div class="grid">
        <div class="section">
            <h3>📊 Current Data Status</h3>
            <button onclick="checkDataStatus()">Check Data Status</button>
            <div id="dataStatus"></div>
        </div>

        <div class="section">
            <h3>✅ Test Pages</h3>
            <button onclick="window.open('/dashboard/appointments', '_blank')">Test Appointments</button>
            <button onclick="window.open('/dashboard/services', '_blank')">Test Services</button>
            <button onclick="window.open('/login', '_blank')">Login Page</button>
        </div>
    </div>

    <script>
        function setupCompleteData() {
            const setupResults = document.getElementById('setupResults');
            
            try {
                setupResults.innerHTML = '<p class="info">🔄 Setting up complete D-Ring Road data...</p>';
                
                // Clear existing data
                localStorage.clear();
                
                // 1. Setup staff user
                const staffUser = {
                    id: "staff-real-1",
                    name: "Mekdes Abebe",
                    email: "<EMAIL>",
                    role: "staff",
                    locations: ["loc1"] // Only D-Ring Road
                };
                localStorage.setItem('vanity_user', JSON.stringify(staffUser));
                localStorage.setItem('vanity_location', 'loc1');
                
                // 2. Setup staff data
                const staffData = [
                    {
                        id: "staff-real-1",
                        name: "Mekdes Abebe",
                        email: "<EMAIL>",
                        phone: "+974 5555 1234",
                        locations: ["loc1"], // D-Ring Road
                        status: "Active",
                        avatar: "MA",
                        color: "bg-purple-100 text-purple-800",
                        employeeNumber: "9100",
                        dateOfBirth: "15-03-90",
                        qidValidity: "31-12-25",
                        passportValidity: "15-06-30",
                        medicalValidity: "20-03-24",
                        profileImage: "",
                        profileImageType: "",
                        role: "stylist",
                        homeService: false
                    }
                ];
                localStorage.setItem('vanity_staff', JSON.stringify(staffData));
                
                // 3. Setup location data
                const locationData = [
                    {
                        id: "loc1",
                        name: "D-ring road",
                        address: "123 D-Ring Road",
                        city: "Doha",
                        country: "Qatar",
                        phone: "(*************",
                        email: "<EMAIL>",
                        status: "Active",
                        enableOnlineBooking: true
                    },
                    {
                        id: "loc2",
                        name: "Muaither",
                        address: "456 Muaither St",
                        city: "Doha",
                        country: "Qatar",
                        phone: "(*************",
                        email: "<EMAIL>",
                        status: "Active",
                        enableOnlineBooking: true
                    },
                    {
                        id: "loc3",
                        name: "Medinat Khalifa",
                        address: "789 Medinat Khalifa Blvd",
                        city: "Doha",
                        country: "Qatar",
                        phone: "(*************",
                        email: "<EMAIL>",
                        status: "Active",
                        enableOnlineBooking: true
                    },
                    {
                        id: "home",
                        name: "Home service",
                        address: "Mobile Service",
                        city: "Doha",
                        country: "Qatar",
                        phone: "(*************",
                        email: "<EMAIL>",
                        status: "Active",
                        enableOnlineBooking: true
                    },
                    {
                        id: "online",
                        name: "Online store",
                        address: "Online",
                        city: "Doha",
                        country: "Qatar",
                        phone: "(*************",
                        email: "<EMAIL>",
                        status: "Active",
                        enableOnlineBooking: true
                    }
                ];
                localStorage.setItem('vanity_locations', JSON.stringify(locationData));
                
                // 4. Setup appointments for D-Ring Road
                const today = new Date();
                const tomorrow = new Date(today);
                tomorrow.setDate(today.getDate() + 1);
                
                const appointments = [
                    {
                        id: "apt-dring-1",
                        bookingReference: "VH-DRING-001",
                        clientId: "client-1",
                        clientName: "Sarah Ahmed",
                        clientEmail: "<EMAIL>",
                        staffId: "staff-real-1",
                        staffName: "Mekdes Abebe",
                        service: "Haircut & Style",
                        serviceId: "service-1",
                        date: new Date(today.setHours(10, 0, 0, 0)).toISOString(),
                        duration: 60,
                        location: "loc1", // D-Ring Road
                        price: 75,
                        status: "confirmed",
                        notes: "Regular client",
                        createdAt: new Date().toISOString(),
                        updatedAt: new Date().toISOString()
                    },
                    {
                        id: "apt-dring-2",
                        bookingReference: "VH-DRING-002",
                        clientId: "client-2",
                        clientName: "Fatima Al-Zahra",
                        clientEmail: "<EMAIL>",
                        staffId: "staff-real-1",
                        staffName: "Mekdes Abebe",
                        service: "Color & Highlights",
                        serviceId: "service-2",
                        date: new Date(today.setHours(14, 0, 0, 0)).toISOString(),
                        duration: 120,
                        location: "loc1", // D-Ring Road
                        price: 150,
                        status: "confirmed",
                        notes: "First time color",
                        createdAt: new Date().toISOString(),
                        updatedAt: new Date().toISOString()
                    },
                    {
                        id: "apt-dring-3",
                        bookingReference: "VH-DRING-003",
                        clientId: "client-3",
                        clientName: "Aisha Mohammed",
                        clientEmail: "<EMAIL>",
                        staffId: "staff-real-1",
                        staffName: "Mekdes Abebe",
                        service: "Facial Treatment",
                        serviceId: "service-3",
                        date: new Date(tomorrow.setHours(11, 0, 0, 0)).toISOString(),
                        duration: 90,
                        location: "loc1", // D-Ring Road
                        price: 100,
                        status: "confirmed",
                        notes: "Sensitive skin",
                        createdAt: new Date().toISOString(),
                        updatedAt: new Date().toISOString()
                    }
                ];
                localStorage.setItem('vanity_appointments', JSON.stringify(appointments));
                
                // 5. Setup services for D-Ring Road
                const services = [
                    {
                        id: "service-1",
                        name: "Haircut & Style",
                        description: "Professional haircut and styling",
                        duration: 60,
                        price: 75,
                        category: "Hair",
                        locations: ["loc1", "loc2", "loc3"],
                        isActive: true
                    },
                    {
                        id: "service-2",
                        name: "Color & Highlights",
                        description: "Hair coloring and highlighting",
                        duration: 120,
                        price: 150,
                        category: "Hair",
                        locations: ["loc1", "loc2", "loc3"],
                        isActive: true
                    },
                    {
                        id: "service-3",
                        name: "Facial Treatment",
                        description: "Deep cleansing facial treatment",
                        duration: 90,
                        price: 100,
                        category: "Skincare",
                        locations: ["loc1", "loc2", "loc3"],
                        isActive: true
                    }
                ];
                localStorage.setItem('vanity_services', JSON.stringify(services));
                
                setupResults.innerHTML = `
                    <div class="success">
                        <h4>✅ Complete D-Ring Road Data Setup Complete!</h4>
                        <ul>
                            <li>✅ User: Mekdes Abebe (staff role, loc1 only)</li>
                            <li>✅ Staff: 1 staff member assigned to D-Ring Road</li>
                            <li>✅ Appointments: 3 appointments for D-Ring Road</li>
                            <li>✅ Services: 3 services available at D-Ring Road</li>
                            <li>✅ Locations: 5 locations configured</li>
                        </ul>
                        <p><strong>Next:</strong> Refresh the appointments page to see the data!</p>
                    </div>
                `;
            } catch (error) {
                setupResults.innerHTML = `<p class="error">Error: ${error.message}</p>`;
            }
        }

        function setupUserOnly() {
            const setupResults = document.getElementById('setupResults');
            
            try {
                const staffUser = {
                    id: "staff-real-1",
                    name: "Mekdes Abebe",
                    email: "<EMAIL>",
                    role: "staff",
                    locations: ["loc1"]
                };
                
                localStorage.setItem('vanity_user', JSON.stringify(staffUser));
                localStorage.setItem('vanity_location', 'loc1');
                
                setupResults.innerHTML = '<p class="success">✅ Staff user setup complete</p>';
            } catch (error) {
                setupResults.innerHTML = `<p class="error">Error: ${error.message}</p>`;
            }
        }

        function setupAppointmentsOnly() {
            // Implementation for appointments only setup
            setupResults.innerHTML = '<p class="info">Setting up appointments only...</p>';
            // Add appointment setup logic here
        }

        function clearAllData() {
            const setupResults = document.getElementById('setupResults');
            
            try {
                localStorage.clear();
                setupResults.innerHTML = '<p class="success">✅ All data cleared</p>';
            } catch (error) {
                setupResults.innerHTML = `<p class="error">Error: ${error.message}</p>`;
            }
        }

        function checkDataStatus() {
            const dataStatus = document.getElementById('dataStatus');
            
            try {
                const user = localStorage.getItem('vanity_user');
                const staff = localStorage.getItem('vanity_staff');
                const appointments = localStorage.getItem('vanity_appointments');
                const services = localStorage.getItem('vanity_services');
                const locations = localStorage.getItem('vanity_locations');
                
                let status = '<h4>Data Status:</h4><ul>';
                
                if (user) {
                    const userData = JSON.parse(user);
                    status += `<li>✅ User: ${userData.name} (${userData.role})</li>`;
                } else {
                    status += '<li>❌ No user data</li>';
                }
                
                if (staff) {
                    const staffData = JSON.parse(staff);
                    status += `<li>✅ Staff: ${staffData.length} members</li>`;
                } else {
                    status += '<li>❌ No staff data</li>';
                }
                
                if (appointments) {
                    const appointmentData = JSON.parse(appointments);
                    status += `<li>✅ Appointments: ${appointmentData.length} appointments</li>`;
                } else {
                    status += '<li>❌ No appointment data</li>';
                }
                
                if (services) {
                    const serviceData = JSON.parse(services);
                    status += `<li>✅ Services: ${serviceData.length} services</li>`;
                } else {
                    status += '<li>❌ No service data</li>';
                }
                
                if (locations) {
                    const locationData = JSON.parse(locations);
                    status += `<li>✅ Locations: ${locationData.length} locations</li>`;
                } else {
                    status += '<li>❌ No location data</li>';
                }
                
                status += '</ul>';
                dataStatus.innerHTML = status;
            } catch (error) {
                dataStatus.innerHTML = `<p class="error">Error: ${error.message}</p>`;
            }
        }

        // Auto-check on page load
        window.onload = function() {
            checkDataStatus();
        };
    </script>
</body>
</html>
