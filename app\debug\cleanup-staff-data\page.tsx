"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { toast } from "@/hooks/use-toast"

interface CleanupResults {
  success: boolean
  message: string
  results: {
    invalidAssignmentsRemoved: number
    staffUpdated: number
    errors: string[]
  }
  finalStats: {
    [key: string]: number
  }
}

export default function CleanupStaffDataPage() {
  const [isRunning, setIsRunning] = useState(false)
  const [results, setResults] = useState<CleanupResults | null>(null)

  const runCleanup = async () => {
    setIsRunning(true)
    setResults(null)
    
    try {
      console.log("🧹 Starting staff data cleanup...")
      
      const response = await fetch('/api/cleanup-staff-data', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      })
      
      const result = await response.json()
      
      if (result.success) {
        console.log("✅ Staff data cleanup completed:", result)
        setResults(result)
        
        toast({
          title: "Cleanup Completed",
          description: `Removed ${result.results.invalidAssignmentsRemoved} invalid assignments and updated ${result.results.staffUpdated} staff members.`,
        })
        
      } else {
        console.error("❌ Cleanup failed:", result.error)
        toast({
          variant: "destructive",
          title: "Cleanup Failed",
          description: result.error || "Failed to cleanup staff data",
        })
      }
      
    } catch (error) {
      console.error("❌ Error during cleanup:", error)
      toast({
        variant: "destructive",
        title: "Error",
        description: "An unexpected error occurred during cleanup",
      })
    } finally {
      setIsRunning(false)
    }
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div>
        <h1 className="text-3xl font-bold">Staff Data Cleanup</h1>
        <p className="text-muted-foreground">
          Clean up invalid location assignments and ensure proper staff-location relationships
        </p>
      </div>
      
      <div className="space-y-4">
        <div className="bg-yellow-50 p-4 rounded-lg">
          <h2 className="text-lg font-semibold mb-2">What this cleanup will do:</h2>
          <ul className="list-disc list-inside space-y-1 text-sm">
            <li>🗑️ <strong>Remove invalid location assignments</strong> (old/invalid location IDs)</li>
            <li>✅ <strong>Assign staff without locations</strong> to default location (D-ring road)</li>
            <li>📊 <strong>Provide cleanup statistics</strong> showing final staff distribution</li>
            <li>🔍 <strong>Identify and report errors</strong> during the cleanup process</li>
          </ul>
        </div>
        
        <div className="flex gap-4">
          <Button
            onClick={runCleanup}
            disabled={isRunning}
            className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
          >
            {isRunning ? "Running Cleanup..." : "Run Staff Data Cleanup"}
          </Button>
        </div>
      </div>

      {results && (
        <div className="space-y-6">
          <h2 className="text-xl font-semibold">Cleanup Results</h2>
          
          <Card>
            <CardHeader>
              <CardTitle>Cleanup Summary</CardTitle>
              <CardDescription>Results of the staff data cleanup operation</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="p-4 border rounded-lg">
                  <h3 className="font-semibold text-red-600">Invalid Assignments Removed</h3>
                  <p className="text-2xl font-bold">{results.results.invalidAssignmentsRemoved}</p>
                </div>
                <div className="p-4 border rounded-lg">
                  <h3 className="font-semibold text-green-600">Staff Updated</h3>
                  <p className="text-2xl font-bold">{results.results.staffUpdated}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Final Staff Distribution</CardTitle>
              <CardDescription>Staff count per location after cleanup</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                {Object.entries(results.finalStats).map(([location, count]) => (
                  <div key={location} className="p-4 border rounded-lg">
                    <h3 className="font-semibold">{location}</h3>
                    <p className="text-2xl font-bold text-blue-600">{count} staff</p>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {results.results.errors.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="text-red-600">Errors</CardTitle>
                <CardDescription>Issues encountered during cleanup</CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2">
                  {results.results.errors.map((error, index) => (
                    <li key={index} className="text-sm text-red-600 bg-red-50 p-2 rounded">
                      {error}
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          )}
        </div>
      )}

      <div className="space-y-4">
        <h2 className="text-xl font-semibold">Next Steps</h2>
        <div className="bg-blue-50 p-4 rounded-lg">
          <ol className="list-decimal list-inside space-y-2 text-sm">
            <li><strong>Run the cleanup</strong> to remove invalid location assignments</li>
            <li><strong>Check the results</strong> to see how many assignments were cleaned up</li>
            <li><strong>Go to the staff location fix page</strong> to assign staff to proper locations</li>
            <li><strong>Test the calendar</strong> to verify staff appear in correct locations</li>
          </ol>
        </div>
      </div>
    </div>
  )
}
