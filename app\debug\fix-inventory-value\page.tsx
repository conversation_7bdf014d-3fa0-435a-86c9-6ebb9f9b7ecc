"use client"

import { useState } from "react"
import { useToast } from "@/components/ui/use-toast"

export default function FixInventoryValuePage() {
  const [isSeeding, setIsSeeding] = useState(false)
  const [seedResults, setSeedResults] = useState<any>(null)
  const [testResults, setTestResults] = useState<any>(null)
  const { toast } = useToast()

  const seedInventoryData = async () => {
    setIsSeeding(true)
    setSeedResults(null)
    
    try {
      console.log("🌱 Starting inventory data seeding...")
      
      const response = await fetch('/api/seed-inventory-data', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      })
      
      const result = await response.json()
      
      if (result.success) {
        console.log("✅ Inventory seeding completed:", result.summary)
        setSeedResults(result)
        
        toast({
          title: "Inventory Seeded",
          description: `Created/updated products with total value of QAR ${result.summary.totalInventoryValue}`,
        })
        
        // Test the inventory value calculation
        await testInventoryValue()
        
      } else {
        console.error("❌ Seeding failed:", result.error)
        toast({
          variant: "destructive",
          title: "Seeding Failed",
          description: result.error || "Failed to seed inventory data",
        })
      }
      
    } catch (error) {
      console.error("❌ Error during seeding:", error)
      toast({
        variant: "destructive",
        title: "Seeding Error",
        description: "An error occurred during inventory seeding",
      })
    } finally {
      setIsSeeding(false)
    }
  }

  const testInventoryValue = async () => {
    try {
      console.log("🧪 Testing inventory value calculation...")
      
      const response = await fetch('/api/inventory/value?locationId=all')
      const result = await response.json()
      
      if (result.success) {
        console.log("✅ Inventory value test completed:", result.inventory)
        setTestResults(result.inventory)
      } else {
        console.error("❌ Inventory value test failed:", result.error)
      }
      
    } catch (error) {
      console.error("❌ Error testing inventory value:", error)
    }
  }

  const goToDashboard = () => {
    window.location.href = '/dashboard'
  }

  const refreshPage = () => {
    window.location.reload()
  }

  return (
    <div className="p-6 space-y-6">
      <h1 className="text-2xl font-bold">Fix Inventory Value Display</h1>
      
      <div className="space-y-4">
        <div className="bg-blue-50 p-4 rounded-lg">
          <h2 className="text-lg font-semibold mb-2">Problem:</h2>
          <p className="text-sm">
            The inventory value card shows QAR 0.00 because there are no products with both cost data and stock quantities in the database.
          </p>
          
          <h2 className="text-lg font-semibold mt-4 mb-2">Solution:</h2>
          <p className="text-sm">
            This tool will seed the database with sample products that have:
          </p>
          <ul className="list-disc list-inside text-sm mt-2 space-y-1">
            <li>✅ Product cost data (required for inventory valuation)</li>
            <li>✅ Stock quantities at multiple locations</li>
            <li>✅ Realistic pricing and categories</li>
            <li>✅ Both retail and professional products</li>
          </ul>
        </div>
        
        <div className="flex gap-4">
          <button
            onClick={seedInventoryData}
            disabled={isSeeding}
            className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
          >
            {isSeeding ? "Seeding..." : "Seed Inventory Data"}
          </button>
          
          <button
            onClick={testInventoryValue}
            className="px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700"
          >
            Test Inventory Value
          </button>
          
          <button
            onClick={refreshPage}
            className="px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
          >
            Refresh Page
          </button>
          
          {(seedResults || testResults) && (
            <button
              onClick={goToDashboard}
              className="px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700"
            >
              Go to Dashboard
            </button>
          )}
        </div>
      </div>

      {seedResults && (
        <div className="space-y-4">
          <h2 className="text-lg font-semibold">Seeding Results:</h2>
          <div className="bg-green-50 p-4 rounded-lg">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p><strong>Products Created:</strong> {seedResults.summary.productsCreated}</p>
                <p><strong>Products Updated:</strong> {seedResults.summary.productsUpdated}</p>
              </div>
              <div>
                <p><strong>Total Inventory Value:</strong> QAR {seedResults.summary.totalInventoryValue}</p>
                <p><strong>Total Items:</strong> {seedResults.summary.totalItems}</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {testResults && (
        <div className="space-y-4">
          <h2 className="text-lg font-semibold">Inventory Value Test Results:</h2>
          <div className="bg-blue-50 p-4 rounded-lg">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p><strong>Total Value:</strong> QAR {testResults.totalValue}</p>
                <p><strong>Total Items:</strong> {testResults.totalItems}</p>
                <p><strong>Products with Value:</strong> {testResults.itemCount}</p>
              </div>
              <div>
                <p><strong>Low Stock Items:</strong> {testResults.lowStockItems}</p>
                <p><strong>Out of Stock Items:</strong> {testResults.outOfStockItems}</p>
                <p><strong>Turnover Rate:</strong> {testResults.turnoverRate}x/year</p>
              </div>
            </div>
            
            {testResults.details && testResults.details.length > 0 && (
              <div className="mt-4">
                <h3 className="font-semibold mb-2">Sample Inventory Items:</h3>
                <div className="space-y-2">
                  {testResults.details.slice(0, 5).map((item: any, index: number) => (
                    <div key={index} className="text-sm bg-white p-2 rounded">
                      <p><strong>{item.productName}</strong> at {item.locationName}</p>
                      <p>{item.stock} units × QAR {item.cost} = QAR {item.value.toFixed(2)}</p>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      <div className="space-y-4">
        <h2 className="text-lg font-semibold">Next Steps:</h2>
        <div className="bg-yellow-50 p-4 rounded-lg">
          <ol className="list-decimal list-inside space-y-1 text-sm">
            <li>Click "Seed Inventory Data" to create products with cost and stock data</li>
            <li>Wait for the seeding to complete (should show total inventory value)</li>
            <li>Click "Test Inventory Value" to verify the calculation works</li>
            <li>Click "Go to Dashboard" to see the updated inventory value card</li>
            <li>The inventory value should now show the actual calculated amount instead of QAR 0.00</li>
          </ol>
        </div>
      </div>

      <div className="space-y-4">
        <h2 className="text-lg font-semibold">Technical Details:</h2>
        <div className="bg-gray-50 p-4 rounded-lg text-sm">
          <p><strong>Inventory Value Formula:</strong> Sum of (Stock Quantity × Product Cost) for all products at all locations</p>
          <p><strong>Dashboard Update:</strong> The stats cards component now fetches inventory data and calculates the value in real-time</p>
          <p><strong>Location Filtering:</strong> The calculation respects the selected location filter (all locations vs specific location)</p>
        </div>
      </div>
    </div>
  )
}
