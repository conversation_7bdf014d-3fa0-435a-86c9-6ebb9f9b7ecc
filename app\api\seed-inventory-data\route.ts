import { NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"

export async function POST() {
  try {
    console.log("🌱 Starting inventory data seeding...")

    // First, ensure we have locations
    const locations = await prisma.location.findMany({
      where: { isActive: true }
    })

    if (locations.length === 0) {
      console.log("📍 Creating default locations...")
      await prisma.location.createMany({
        data: [
          {
            id: 'loc1',
            name: 'D-ring road',
            address: '123 D-Ring Road',
            city: 'Doha',
            state: 'Doha',
            zipCode: '12345',
            country: 'Qatar',
            phone: '(*************',
            email: '<EMAIL>',
            isActive: true
          },
          {
            id: 'loc2',
            name: '<PERSON><PERSON><PERSON>',
            address: '456 Muaither St',
            city: 'Doha',
            state: 'Doha',
            zipCode: '23456',
            country: 'Qatar',
            phone: '(*************',
            email: '<EMAIL>',
            isActive: true
          },
          {
            id: 'online',
            name: 'Online store',
            address: 'Online',
            city: 'Doha',
            state: 'Doha',
            zipCode: '00000',
            country: 'Qatar',
            phone: '(*************',
            email: '<EMAIL>',
            isActive: true
          }
        ],
        skipDuplicates: true
      })
    }

    // Sample products with cost data
    const sampleProducts = [
      {
        name: "Moroccan Argan Oil Shampoo",
        description: "Nourishing shampoo with organic argan oil",
        price: 85.0,
        cost: 35.0,
        category: "HAIR_CARE",
        type: "Shampoo",
        brand: "Moroccan Gold",
        sku: "MG-SHAMP-001",
        isRetail: true,
        stock: 25
      },
      {
        name: "Keratin Hair Treatment",
        description: "Professional keratin smoothing treatment",
        price: 150.0,
        cost: 75.0,
        category: "HAIR_CARE",
        type: "Treatment",
        brand: "Keratin Pro",
        sku: "KP-TREAT-001",
        isRetail: false,
        stock: 15
      },
      {
        name: "Vitamin C Face Serum",
        description: "Brightening vitamin C serum for all skin types",
        price: 120.0,
        cost: 48.0,
        category: "SKINCARE",
        type: "Serum",
        brand: "Glow Beauty",
        sku: "GB-SERUM-001",
        isRetail: true,
        stock: 30
      },
      {
        name: "Professional Hair Dryer",
        description: "Ionic ceramic hair dryer with multiple settings",
        price: 250.0,
        cost: 125.0,
        category: "TOOLS",
        type: "Hair Dryer",
        brand: "Pro Style",
        sku: "PS-DRYER-001",
        isRetail: false,
        stock: 8
      },
      {
        name: "Luxury Nail Polish Set",
        description: "Set of 12 premium nail polish colors",
        price: 95.0,
        cost: 38.0,
        category: "NAIL_CARE",
        type: "Polish Set",
        brand: "Nail Art Pro",
        sku: "NAP-SET-001",
        isRetail: true,
        stock: 20
      },
      {
        name: "Anti-Aging Night Cream",
        description: "Rich night cream with retinol and peptides",
        price: 180.0,
        cost: 72.0,
        category: "SKINCARE",
        type: "Night Cream",
        brand: "Age Reverse",
        sku: "AR-CREAM-001",
        isRetail: true,
        stock: 18
      },
      {
        name: "Hair Color - Chocolate Brown",
        description: "Professional permanent hair color",
        price: 45.0,
        cost: 18.0,
        category: "HAIR_CARE",
        type: "Hair Color",
        brand: "Color Master",
        sku: "CM-COLOR-001",
        isRetail: false,
        stock: 35
      },
      {
        name: "Makeup Brush Set",
        description: "Professional 15-piece makeup brush set",
        price: 160.0,
        cost: 64.0,
        category: "TOOLS",
        type: "Brush Set",
        brand: "Makeup Pro",
        sku: "MP-BRUSH-001",
        isRetail: true,
        stock: 12
      }
    ]

    console.log(`📦 Creating ${sampleProducts.length} sample products...`)

    let createdCount = 0
    let updatedCount = 0

    for (const productData of sampleProducts) {
      // Check if product already exists
      const existingProduct = await prisma.product.findFirst({
        where: { sku: productData.sku }
      })

      let product
      if (existingProduct) {
        // Update existing product with cost data
        product = await prisma.product.update({
          where: { id: existingProduct.id },
          data: {
            cost: productData.cost,
            price: productData.price
          }
        })
        updatedCount++
        console.log(`✅ Updated product: ${product.name}`)
      } else {
        // Create new product
        product = await prisma.product.create({
          data: {
            name: productData.name,
            description: productData.description,
            price: productData.price,
            cost: productData.cost,
            category: productData.category,
            type: productData.type,
            brand: productData.brand,
            sku: productData.sku,
            isRetail: productData.isRetail,
            isActive: true
          }
        })
        createdCount++
        console.log(`✅ Created product: ${product.name}`)
      }

      // Add stock to all locations
      const activeLocations = await prisma.location.findMany({
        where: { isActive: true }
      })

      for (const location of activeLocations) {
        // Check if product-location relationship exists
        const existingProductLocation = await prisma.productLocation.findFirst({
          where: {
            productId: product.id,
            locationId: location.id
          }
        })

        if (existingProductLocation) {
          // Update stock if it's currently 0
          if (existingProductLocation.stock === 0) {
            await prisma.productLocation.update({
              where: { id: existingProductLocation.id },
              data: { stock: productData.stock }
            })
            console.log(`📦 Updated stock for ${product.name} at ${location.name}: ${productData.stock}`)
          }
        } else {
          // Create new product-location relationship
          await prisma.productLocation.create({
            data: {
              productId: product.id,
              locationId: location.id,
              stock: productData.stock,
              isActive: true
            }
          })
          console.log(`📦 Added stock for ${product.name} at ${location.name}: ${productData.stock}`)
        }
      }
    }

    // Calculate total inventory value
    const productLocations = await prisma.productLocation.findMany({
      where: {
        isActive: true,
        product: { isActive: true }
      },
      include: {
        product: {
          select: {
            name: true,
            cost: true
          }
        },
        location: {
          select: {
            name: true
          }
        }
      }
    })

    let totalValue = 0
    let totalItems = 0
    productLocations.forEach(item => {
      if (item.stock > 0 && item.product.cost) {
        const value = item.stock * parseFloat(item.product.cost.toString())
        totalValue += value
        totalItems += item.stock
      }
    })

    console.log("✅ Inventory data seeding completed!")
    console.log(`📊 Summary:`)
    console.log(`   - Products created: ${createdCount}`)
    console.log(`   - Products updated: ${updatedCount}`)
    console.log(`   - Total inventory value: QAR ${totalValue.toFixed(2)}`)
    console.log(`   - Total items in stock: ${totalItems}`)

    return NextResponse.json({
      success: true,
      message: "Inventory data seeded successfully",
      summary: {
        productsCreated: createdCount,
        productsUpdated: updatedCount,
        totalInventoryValue: Math.round(totalValue * 100) / 100,
        totalItems: totalItems,
        productLocations: productLocations.length
      }
    })

  } catch (error) {
    console.error("❌ Error seeding inventory data:", error)
    return NextResponse.json({
      success: false,
      error: "Failed to seed inventory data",
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 })
  }
}
