import { NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"

/**
 * POST /api/assign-staff-by-actual-names
 * 
 * Assigns staff to locations based on their actual names in the database
 */
export async function POST() {
  try {
    console.log("🔧 Starting staff assignment by actual names...")

    // Get all staff members first
    const allStaff = await prisma.staffMember.findMany({
      where: { status: 'ACTIVE' }
    })

    console.log("📋 Available staff members:")
    allStaff.forEach((staff, index) => console.log(`  ${index + 1}. ${staff.name}`))

    // Clear existing assignments
    await prisma.staffLocation.deleteMany({})
    console.log("✅ Cleared all existing assignments")

    // Based on the actual staff names, create assignments
    // D-ring road (loc1) - 8 staff members
    const dRingStaff = [
      "Fat<PERSON>-Zahra",
      "<PERSON>", 
      "<PERSON><PERSON>",
      "<PERSON><PERSON>",
      "<PERSON><PERSON>",
      "<PERSON><PERSON>",
      "<PERSON><PERSON>",
      "<PERSON><PERSON>"
    ]

    // Mu<PERSON><PERSON> (loc2) - 8 staff members  
    const muaither<PERSON>ta<PERSON> = [
      "<PERSON><PERSON>",
      "<PERSON><PERSON>",
      "<PERSON><PERSON><PERSON>",
      "<PERSON><PERSON>",
      "<PERSON>",
      "<PERSON>a <PERSON>bib",
      "Sara Al-Zahra",
      "Mona Kassem"
    ]

    // Medinat Khalifa (loc3) - 2 staff members
    const medinatStaff = [
      "Rana Othman"
    ]

    let assignmentCount = 0

    // Assign D-ring road staff
    for (const staffName of dRingStaff) {
      const staffMember = allStaff.find(staff => staff.name === staffName)
      if (staffMember) {
        await prisma.staffLocation.create({
          data: {
            staffId: staffMember.id,
            locationId: 'loc1',
            isActive: true
          }
        })
        console.log(`✅ Assigned ${staffMember.name} to D-ring road (loc1)`)
        assignmentCount++
      } else {
        console.warn(`⚠️ Staff member not found: ${staffName}`)
      }
    }

    // Assign Muaither staff
    for (const staffName of muaitherStaff) {
      const staffMember = allStaff.find(staff => staff.name === staffName)
      if (staffMember) {
        await prisma.staffLocation.create({
          data: {
            staffId: staffMember.id,
            locationId: 'loc2',
            isActive: true
          }
        })
        console.log(`✅ Assigned ${staffMember.name} to Muaither (loc2)`)
        assignmentCount++
      } else {
        console.warn(`⚠️ Staff member not found: ${staffName}`)
      }
    }

    // Assign Medinat Khalifa staff
    for (const staffName of medinatStaff) {
      const staffMember = allStaff.find(staff => staff.name === staffName)
      if (staffMember) {
        await prisma.staffLocation.create({
          data: {
            staffId: staffMember.id,
            locationId: 'loc3',
            isActive: true
          }
        })
        console.log(`✅ Assigned ${staffMember.name} to Medinat Khalifa (loc3)`)
        assignmentCount++
      } else {
        console.warn(`⚠️ Staff member not found: ${staffName}`)
      }
    }

    // Get final counts
    const finalCounts = await Promise.all([
      prisma.staffLocation.count({ where: { locationId: 'loc1', isActive: true } }),
      prisma.staffLocation.count({ where: { locationId: 'loc2', isActive: true } }),
      prisma.staffLocation.count({ where: { locationId: 'loc3', isActive: true } })
    ])

    console.log("✅ Staff assignment completed!")
    console.log(`📊 Final distribution:`)
    console.log(`   D-ring road (loc1): ${finalCounts[0]} staff`)
    console.log(`   Muaither (loc2): ${finalCounts[1]} staff`)
    console.log(`   Medinat Khalifa (loc3): ${finalCounts[2]} staff`)

    return NextResponse.json({
      success: true,
      message: "Staff assigned successfully by actual names",
      summary: {
        totalAssignments: assignmentCount,
        locationCounts: {
          "D-ring road (loc1)": finalCounts[0],
          "Muaither (loc2)": finalCounts[1],
          "Medinat Khalifa (loc3)": finalCounts[2]
        }
      }
    })

  } catch (error) {
    console.error("❌ Error during staff assignment:", error)
    return NextResponse.json({
      success: false,
      error: "Failed to assign staff by actual names",
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 })
  }
}
