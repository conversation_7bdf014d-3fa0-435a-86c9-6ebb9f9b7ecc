"use client"

import { useAuth } from "@/lib/auth-provider"
import { useRouter } from "next/navigation"
import { useEffect } from "react"

interface RoleGuardProps {
  children: React.ReactNode
  allowedRoles?: string[]
  blockedRoles?: string[]
  redirectTo?: string
  fallback?: React.ReactNode
}

export function RoleGuard({ 
  children, 
  allowedRoles, 
  blockedRoles, 
  redirectTo = "/dashboard", 
  fallback 
}: RoleGuardProps) {
  const { user, isAuthenticated } = useAuth()
  const router = useRouter()

  useEffect(() => {
    if (!isAuthenticated || !user) {
      router.push("/auth/signin")
      return
    }

    // Check if user role is blocked
    if (blockedRoles && blockedRoles.includes(user.role)) {
      console.log(`🚫 Access denied for role: ${user.role}`)
      router.push(redirectTo)
      return
    }

    // Check if user role is allowed (if allowedRoles is specified)
    if (allowedRoles && !allowedRoles.includes(user.role)) {
      console.log(`🚫 Access denied for role: ${user.role}. Allowed roles: ${allowedRoles.join(', ')}`)
      router.push(redirectTo)
      return
    }
  }, [user, isAuthenticated, allowedRoles, blockedRoles, redirectTo, router])

  // Show loading or fallback while checking authentication
  if (!isAuthenticated || !user) {
    return fallback || <div className="p-6">Loading...</div>
  }

  // Check if user role is blocked
  if (blockedRoles && blockedRoles.includes(user.role)) {
    return fallback || <div className="p-6">Access denied</div>
  }

  // Check if user role is allowed (if allowedRoles is specified)
  if (allowedRoles && !allowedRoles.includes(user.role)) {
    return fallback || <div className="p-6">Access denied</div>
  }

  return <>{children}</>
}

// Specific guards for common use cases
export function AdminOnlyGuard({ children, fallback }: { children: React.ReactNode, fallback?: React.ReactNode }) {
  return (
    <RoleGuard 
      allowedRoles={['super_admin', 'org_admin', 'admin']} 
      fallback={fallback}
    >
      {children}
    </RoleGuard>
  )
}

export function NoReceptionistGuard({ children, fallback }: { children: React.ReactNode, fallback?: React.ReactNode }) {
  return (
    <RoleGuard 
      blockedRoles={['RECEPTIONIST', 'receptionist']} 
      fallback={fallback}
    >
      {children}
    </RoleGuard>
  )
}

export function ReceptionistOnlyGuard({ children, fallback }: { children: React.ReactNode, fallback?: React.ReactNode }) {
  return (
    <RoleGuard 
      allowedRoles={['RECEPTIONIST', 'receptionist']} 
      fallback={fallback}
    >
      {children}
    </RoleGuard>
  )
}

// Permission-based guard
export function PermissionGuard({ 
  children, 
  permission, 
  permissions, 
  requireAll = false,
  fallback 
}: { 
  children: React.ReactNode
  permission?: string
  permissions?: string[]
  requireAll?: boolean
  fallback?: React.ReactNode 
}) {
  const { hasPermission, hasAnyPermission } = useAuth()

  let hasAccess = false

  if (permission) {
    hasAccess = hasPermission(permission)
  } else if (permissions) {
    hasAccess = requireAll 
      ? permissions.every(p => hasPermission(p))
      : hasAnyPermission(permissions)
  }

  if (!hasAccess) {
    return fallback || <div className="p-6">Access denied - insufficient permissions</div>
  }

  return <>{children}</>
}

// Location access guard
export function LocationAccessGuard({ 
  children, 
  locationId, 
  fallback 
}: { 
  children: React.ReactNode
  locationId: string
  fallback?: React.ReactNode 
}) {
  const { canAccessLocation } = useAuth()

  if (!canAccessLocation(locationId)) {
    return fallback || <div className="p-6">Access denied - location not accessible</div>
  }

  return <>{children}</>
}
