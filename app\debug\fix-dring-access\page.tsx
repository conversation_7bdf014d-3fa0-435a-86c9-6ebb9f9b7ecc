"use client"

import { useState } from "react"
import { useToast } from "@/components/ui/use-toast"

export default function FixDRingAccessPage() {
  const [isFixing, setIsFixing] = useState(false)
  const [results, setResults] = useState<string[]>([])
  const { toast } = useToast()

  const addResult = (message: string) => {
    setResults(prev => [...prev, message])
    console.log(message)
  }

  const fixDRingAccess = async () => {
    setIsFixing(true)
    setResults([])
    
    try {
      addResult("🔧 Starting D-Ring Road access fix...")

      // 1. Check current user and set admin access
      addResult("1️⃣ Setting up admin user with full access...")
      const adminUser = {
        id: "admin-1",
        name: "Admin User",
        email: "<EMAIL>",
        role: "org_admin",
        locations: ["all"] // Full access to all locations
      }
      localStorage.setItem('vanity_user', JSON.stringify(adminUser))
      localStorage.setItem('vanity_location', 'all')
      addResult("✅ Admin user set with full location access")

      // 2. Ensure D-Ring Road location exists in database
      addResult("2️⃣ Checking D-Ring Road location in database...")
      try {
        const locationsResponse = await fetch('/api/locations')
        const locationsData = await locationsResponse.json()
        const dRingLocation = locationsData.locations?.find((loc: any) => 
          loc.name.toLowerCase().includes('d-ring') || loc.id === 'loc1'
        )
        
        if (dRingLocation) {
          addResult(`✅ D-Ring Road location found: ${dRingLocation.name} (${dRingLocation.id})`)
        } else {
          addResult("❌ D-Ring Road location not found in database")
          // Create D-Ring Road location
          addResult("🔧 Creating D-Ring Road location...")
          const createResponse = await fetch('/api/locations', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              id: 'loc1',
              name: 'D-ring road',
              address: '123 D-Ring Road',
              city: 'Doha',
              state: 'Doha',
              zipCode: '12345',
              country: 'Qatar',
              phone: '(*************',
              email: '<EMAIL>'
            })
          })
          
          if (createResponse.ok) {
            addResult("✅ D-Ring Road location created successfully")
          } else {
            addResult("❌ Failed to create D-Ring Road location")
          }
        }
      } catch (error) {
        addResult(`❌ Error checking locations: ${error}`)
      }

      // 3. Check and create D-Ring Road staff
      addResult("3️⃣ Checking D-Ring Road staff...")
      try {
        const staffResponse = await fetch('/api/staff')
        const staffData = await staffResponse.json()
        const dRingStaff = staffData.staff?.filter((staff: any) => 
          staff.locations?.includes('loc1')
        )
        
        addResult(`📊 Found ${dRingStaff?.length || 0} staff members assigned to D-Ring Road`)
        
        if (!dRingStaff || dRingStaff.length === 0) {
          addResult("🔧 Creating Mekdes Abebe for D-Ring Road...")
          
          // First create user account
          const userResponse = await fetch('/api/auth/register', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              name: 'Mekdes Abebe',
              email: '<EMAIL>',
              password: 'password123',
              role: 'staff'
            })
          })
          
          if (userResponse.ok) {
            const userData = await userResponse.json()
            addResult("✅ User account created for Mekdes Abebe")
            
            // Then create staff member
            const staffCreateResponse = await fetch('/api/staff', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({
                name: 'Mekdes Abebe',
                email: '<EMAIL>',
                phone: '+974 5555 1234',
                role: 'stylist',
                locations: ['loc1'], // D-Ring Road only
                status: 'Active',
                employeeNumber: '9100',
                dateOfBirth: '1990-03-15',
                homeService: false
              })
            })
            
            if (staffCreateResponse.ok) {
              addResult("✅ Mekdes Abebe staff member created and assigned to D-Ring Road")
            } else {
              addResult("❌ Failed to create staff member")
            }
          } else {
            addResult("❌ Failed to create user account")
          }
        } else {
          addResult("✅ D-Ring Road staff already exists")
          dRingStaff.forEach((staff: any) => {
            addResult(`   - ${staff.name} (${staff.email})`)
          })
        }
      } catch (error) {
        addResult(`❌ Error checking staff: ${error}`)
      }

      // 4. Create sample appointments for D-Ring Road
      addResult("4️⃣ Creating sample appointments for D-Ring Road...")
      const today = new Date()
      const tomorrow = new Date(today)
      tomorrow.setDate(today.getDate() + 1)
      
      const sampleAppointments = [
        {
          id: `dring-apt-${Date.now()}-1`,
          clientName: "Sarah Ahmed",
          clientId: "client-sarah-1",
          service: "Hair Cut & Style",
          serviceId: "service-1",
          staffId: "staff-real-1",
          staffName: "Mekdes Abebe",
          date: tomorrow.toISOString().split('T')[0],
          time: "10:00",
          duration: 60,
          price: 150,
          status: "confirmed",
          location: "loc1", // D-Ring Road
          bookingReference: `DR${Date.now()}1`,
          notes: "Regular client, prefers layered cut"
        },
        {
          id: `dring-apt-${Date.now()}-2`,
          clientName: "Fatima Al-Rashid",
          clientId: "client-fatima-1",
          service: "Hair Color",
          serviceId: "service-2",
          staffId: "staff-real-1",
          staffName: "Mekdes Abebe",
          date: tomorrow.toISOString().split('T')[0],
          time: "14:00",
          duration: 120,
          price: 250,
          status: "pending",
          location: "loc1", // D-Ring Road
          bookingReference: `DR${Date.now()}2`,
          notes: "First time color, consultation needed"
        }
      ]
      
      // Save appointments to localStorage
      const existingAppointments = JSON.parse(localStorage.getItem('vanity_appointments') || '[]')
      const updatedAppointments = [...existingAppointments, ...sampleAppointments]
      localStorage.setItem('vanity_appointments', JSON.stringify(updatedAppointments))
      addResult(`✅ Created ${sampleAppointments.length} sample appointments for D-Ring Road`)

      // 5. Force refresh location and staff data
      addResult("5️⃣ Refreshing location and staff data...")
      
      // Trigger location refresh
      try {
        await fetch('/api/locations?refresh=true')
        addResult("✅ Location data refreshed")
      } catch (error) {
        addResult(`⚠️ Location refresh warning: ${error}`)
      }

      addResult("🎉 D-Ring Road access fix completed!")
      addResult("📋 Next steps:")
      addResult("   1. Refresh the appointments page")
      addResult("   2. Select 'D-ring road' from the location selector")
      addResult("   3. You should now see Mekdes Abebe and appointments")
      
      toast({
        title: "Fix Completed",
        description: "D-Ring Road access has been configured. Please refresh the appointments page.",
      })

    } catch (error) {
      addResult(`❌ Error during fix: ${error}`)
      toast({
        variant: "destructive",
        title: "Fix Failed",
        description: "There was an error fixing D-Ring Road access.",
      })
    } finally {
      setIsFixing(false)
    }
  }

  return (
    <div className="p-6 space-y-6">
      <h1 className="text-2xl font-bold">Fix D-Ring Road Access</h1>
      
      <div className="space-y-4">
        <p className="text-gray-600">
          This tool will fix the D-Ring Road location visibility and staff access issues.
        </p>
        
        <button
          onClick={fixDRingAccess}
          disabled={isFixing}
          className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
        >
          {isFixing ? "Fixing..." : "Fix D-Ring Road Access"}
        </button>
      </div>

      {results.length > 0 && (
        <div className="space-y-2">
          <h2 className="text-lg font-semibold">Fix Results:</h2>
          <div className="bg-gray-100 p-4 rounded-lg max-h-96 overflow-y-auto">
            {results.map((result, index) => (
              <div key={index} className="text-sm font-mono">
                {result}
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}
