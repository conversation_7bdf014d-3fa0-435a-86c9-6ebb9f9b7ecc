"use client"

import { useState } from "react"
import { useToast } from "@/components/ui/use-toast"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"

export default function SetupReceptionistUsersPage() {
  const [isSetupRunning, setIsSetupRunning] = useState(false)
  const [setupResults, setSetupResults] = useState<any>(null)
  const { toast } = useToast()

  const runSetup = async () => {
    setIsSetupRunning(true)
    setSetupResults(null)
    
    try {
      console.log("👥 Starting receptionist users setup...")
      
      const response = await fetch('/api/setup-receptionist-users', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      })
      
      const result = await response.json()
      
      if (result.success) {
        console.log("✅ Receptionist setup completed:", result)
        setSetupResults(result)
        
        toast({
          title: "Setup Completed",
          description: `Created ${result.summary.usersCreated} receptionist users with location-based access`,
        })
        
      } else {
        console.error("❌ Setup failed:", result.error)
        toast({
          variant: "destructive",
          title: "Setup Failed",
          description: result.error || "Failed to setup receptionist users",
        })
      }
      
    } catch (error) {
      console.error("❌ Error during setup:", error)
      toast({
        variant: "destructive",
        title: "Setup Error",
        description: "An error occurred during setup",
      })
    } finally {
      setIsSetupRunning(false)
    }
  }

  const copyCredentials = (user: any) => {
    const credentials = `Email: ${user.email}\nPassword: ${user.password}\nLocation: ${user.location}`
    navigator.clipboard.writeText(credentials)
    toast({
      title: "Credentials Copied",
      description: `${user.name}'s login credentials copied to clipboard`,
    })
  }

  const testLogin = (email: string) => {
    // Open login page with email pre-filled
    const loginUrl = `/auth/signin?email=${encodeURIComponent(email)}`
    window.open(loginUrl, '_blank')
  }

  return (
    <div className="p-6 space-y-6">
      <h1 className="text-2xl font-bold">Setup Receptionist Demo Users</h1>
      
      <div className="space-y-4">
        <div className="bg-blue-50 p-4 rounded-lg">
          <h2 className="text-lg font-semibold mb-2">What this setup will create:</h2>
          <ul className="list-disc list-inside space-y-1 text-sm">
            <li>✅ <strong>3 Receptionist Users</strong> with location-specific access</li>
            <li>✅ <strong>Location Setup</strong> for D-ring road, Muaither, and Medinat Khalifa</li>
            <li>✅ <strong>Staff Members</strong> assigned to their respective locations</li>
            <li>✅ <strong>Sample Appointments</strong> for each location to test access control</li>
            <li>✅ <strong>Access Control</strong> ensuring receptionists only see their location's data</li>
          </ul>
        </div>
        
        <div className="flex gap-4">
          <Button
            onClick={runSetup}
            disabled={isSetupRunning}
            className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
          >
            {isSetupRunning ? "Setting up..." : "Create Receptionist Users"}
          </Button>
          
          {setupResults && (
            <Button
              onClick={() => window.location.href = '/auth/signin'}
              className="px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700"
            >
              Go to Login Page
            </Button>
          )}
        </div>
      </div>

      {setupResults && (
        <div className="space-y-6">
          <h2 className="text-xl font-semibold">Receptionist User Credentials</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {setupResults.users.map((user: any, index: number) => (
              <Card key={index} className="border-2">
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    {user.name}
                    <Badge variant="secondary">{user.role}</Badge>
                  </CardTitle>
                  <CardDescription>{user.location}</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <div>
                      <strong>Email:</strong>
                      <div className="font-mono text-sm bg-gray-100 p-2 rounded">
                        {user.email}
                      </div>
                    </div>
                    <div>
                      <strong>Password:</strong>
                      <div className="font-mono text-sm bg-gray-100 p-2 rounded">
                        {user.password}
                      </div>
                    </div>
                    <div>
                      <strong>Phone:</strong>
                      <div className="text-sm">{user.phone}</div>
                    </div>
                    <div>
                      <strong>Location ID:</strong>
                      <div className="text-sm font-mono">{user.locationId}</div>
                    </div>
                  </div>
                  
                  <div className="flex gap-2">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => copyCredentials(user)}
                      className="flex-1"
                    >
                      Copy Credentials
                    </Button>
                    <Button
                      size="sm"
                      onClick={() => testLogin(user.email)}
                      className="flex-1"
                    >
                      Test Login
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          <div className="bg-green-50 p-4 rounded-lg">
            <h3 className="font-semibold mb-2">Setup Summary:</h3>
            <div className="grid grid-cols-3 gap-4 text-sm">
              <div>
                <strong>Users Created:</strong> {setupResults.summary.usersCreated}
              </div>
              <div>
                <strong>Locations Setup:</strong> {setupResults.summary.locationsSetup}
              </div>
              <div>
                <strong>Sample Appointments:</strong> {setupResults.summary.appointmentsCreated}
              </div>
            </div>
          </div>
        </div>
      )}

      <div className="space-y-4">
        <h2 className="text-xl font-semibold">Access Control Features</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="bg-yellow-50 p-4 rounded-lg">
            <h3 className="font-semibold mb-2">What Receptionists CAN Access:</h3>
            <ul className="list-disc list-inside space-y-1 text-sm">
              <li>✅ Appointments for their assigned location only</li>
              <li>✅ Client management for their location</li>
              <li>✅ Staff schedules for their location</li>
              <li>✅ Services and pricing</li>
              <li>✅ Basic inventory view for their location</li>
              <li>✅ Reports filtered to their location</li>
            </ul>
          </div>
          
          <div className="bg-red-50 p-4 rounded-lg">
            <h3 className="font-semibold mb-2">What Receptionists CANNOT Access:</h3>
            <ul className="list-disc list-inside space-y-1 text-sm">
              <li>❌ Other locations' appointments or data</li>
              <li>❌ Financial reports and revenue data</li>
              <li>❌ Staff management (hiring, firing, etc.)</li>
              <li>❌ System settings and configuration</li>
              <li>❌ Inventory management (adding/removing stock)</li>
              <li>❌ Location switching options</li>
            </ul>
          </div>
        </div>
      </div>

      <div className="space-y-4">
        <h2 className="text-xl font-semibold">Testing Instructions</h2>
        <div className="bg-blue-50 p-4 rounded-lg">
          <ol className="list-decimal list-inside space-y-2 text-sm">
            <li><strong>Run the setup</strong> by clicking "Create Receptionist Users"</li>
            <li><strong>Copy credentials</strong> for any receptionist user</li>
            <li><strong>Open login page</strong> and sign in with receptionist credentials</li>
            <li><strong>Verify location restriction:</strong> User should only see their assigned location</li>
            <li><strong>Test appointments:</strong> Should only see appointments for their location</li>
            <li><strong>Test navigation:</strong> Some admin features should be hidden/restricted</li>
            <li><strong>Switch users:</strong> Try logging in as different receptionists to see different location data</li>
          </ol>
        </div>
      </div>

      <div className="space-y-4">
        <h2 className="text-xl font-semibold">Location Details</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="font-semibold">D-ring road (loc1)</h3>
            <p className="text-sm">Receptionist: Sarah Al-Rashid</p>
            <p className="text-sm">Phone: +974 5555 1001</p>
            <p className="text-sm">Sample appointments: 2</p>
          </div>
          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="font-semibold">Muaither (loc2)</h3>
            <p className="text-sm">Receptionist: Fatima Al-Kuwari</p>
            <p className="text-sm">Phone: +974 5555 1002</p>
            <p className="text-sm">Sample appointments: 2</p>
          </div>
          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="font-semibold">Medinat Khalifa (loc3)</h3>
            <p className="text-sm">Receptionist: Aisha Al-Thani</p>
            <p className="text-sm">Phone: +974 5555 1003</p>
            <p className="text-sm">Sample appointments: 2</p>
          </div>
        </div>
      </div>
    </div>
  )
}
