"use client"

import { useState } from "react"
import { useToast } from "@/components/ui/use-toast"

export default function SetupDRingCompletePage() {
  const [isSetupRunning, setIsSetupRunning] = useState(false)
  const [setupResults, setSetupResults] = useState<any>(null)
  const { toast } = useToast()

  const runCompleteSetup = async () => {
    setIsSetupRunning(true)
    setSetupResults(null)
    
    try {
      console.log("🔧 Starting complete D-Ring Road setup...")
      
      // 1. Run the database setup
      const response = await fetch('/api/setup-dring-complete', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      })
      
      const result = await response.json()
      
      if (result.success) {
        console.log("✅ Database setup completed:", result.data)
        
        // 2. Set up admin user in localStorage for full access
        const adminUser = {
          id: "admin-1",
          name: "Admin User",
          email: "<EMAIL>",
          role: "org_admin",
          locations: ["all"] // Full access to all locations
        }
        localStorage.setItem('vanity_user', JSON.stringify(adminUser))
        localStorage.setItem('vanity_location', 'all')
        
        console.log("✅ Admin user configured with full access")
        
        setSetupResults(result)
        
        toast({
          title: "Setup Completed",
          description: "D-Ring Road has been set up successfully. You can now access it in the appointments calendar.",
        })
        
      } else {
        console.error("❌ Setup failed:", result.error)
        toast({
          variant: "destructive",
          title: "Setup Failed",
          description: result.error || "Failed to setup D-Ring Road",
        })
      }
      
    } catch (error) {
      console.error("❌ Error during setup:", error)
      toast({
        variant: "destructive",
        title: "Setup Error",
        description: "An error occurred during setup",
      })
    } finally {
      setIsSetupRunning(false)
    }
  }

  const goToAppointments = () => {
    window.location.href = '/dashboard/appointments'
  }

  const checkData = () => {
    window.location.href = '/debug/check-dring-data'
  }

  return (
    <div className="p-6 space-y-6">
      <h1 className="text-2xl font-bold">Complete D-Ring Road Setup</h1>
      
      <div className="space-y-4">
        <div className="bg-blue-50 p-4 rounded-lg">
          <h2 className="text-lg font-semibold mb-2">What this setup will do:</h2>
          <ul className="list-disc list-inside space-y-1 text-sm">
            <li>✅ Create/verify D-Ring Road location in database</li>
            <li>✅ Create/verify Mekdes Abebe user account</li>
            <li>✅ Create/verify Mekdes Abebe staff member</li>
            <li>✅ Assign Mekdes to D-Ring Road location</li>
            <li>✅ Create sample appointments for D-Ring Road</li>
            <li>✅ Set up admin user with full location access</li>
          </ul>
        </div>
        
        <div className="flex gap-4">
          <button
            onClick={runCompleteSetup}
            disabled={isSetupRunning}
            className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
          >
            {isSetupRunning ? "Setting up..." : "Run Complete Setup"}
          </button>
          
          <button
            onClick={checkData}
            className="px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
          >
            Check Current Data
          </button>
          
          {setupResults && (
            <button
              onClick={goToAppointments}
              className="px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700"
            >
              Go to Appointments
            </button>
          )}
        </div>
      </div>

      {setupResults && (
        <div className="space-y-4">
          <h2 className="text-lg font-semibold">Setup Results:</h2>
          <div className="bg-green-50 p-4 rounded-lg">
            <div className="space-y-2">
              <div>
                <strong>Location:</strong> {setupResults.data.location.name} ({setupResults.data.location.id})
                <span className={`ml-2 px-2 py-1 rounded text-xs ${setupResults.data.location.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                  {setupResults.data.location.isActive ? 'Active' : 'Inactive'}
                </span>
              </div>
              <div>
                <strong>Staff:</strong> {setupResults.data.staff.name} ({setupResults.data.staff.id})
                <span className={`ml-2 px-2 py-1 rounded text-xs ${setupResults.data.staff.status === 'ACTIVE' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                  {setupResults.data.staff.status}
                </span>
              </div>
              <div>
                <strong>Staff Locations:</strong> {setupResults.data.staff.locations?.map((loc: any) => loc.name).join(', ')}
              </div>
              <div>
                <strong>Appointments:</strong> {setupResults.data.appointmentCount} upcoming appointments
              </div>
            </div>
          </div>
          
          <div className="bg-yellow-50 p-4 rounded-lg">
            <h3 className="font-semibold mb-2">Next Steps:</h3>
            <ol className="list-decimal list-inside space-y-1 text-sm">
              <li>Click "Go to Appointments" to open the appointments calendar</li>
              <li>In the location selector, choose "D-ring road"</li>
              <li>You should now see Mekdes Abebe in the staff list</li>
              <li>You should see the sample appointments for tomorrow</li>
              <li>The calendar should display properly with D-Ring Road data</li>
            </ol>
          </div>
        </div>
      )}
    </div>
  )
}
