"use client"

import React from "react"

import { useState } from "react"
import { useAuth } from "@/lib/auth-provider"
import { But<PERSON> } from "@/components/ui/button"
import { Ta<PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { ChevronLeft, ChevronRight, CalendarIcon, ListIcon, Plus } from "lucide-react"
import { format, addDays, subDays, parseISO } from "date-fns"
import { cn } from "@/lib/utils"
import { getAllAppointments } from "@/lib/appointment-service"
import { useStaff } from "@/lib/staff-provider"
import { StaffAvatar } from "@/components/ui/staff-avatar"
import { getFirstName } from "@/lib/female-avatars"

interface SalonCalendarViewProps {
  onAddAppointment?: () => void
}

export function SalonCalendarView({ onAddAppointment }: SalonCalendarViewProps) {
  const { currentLocation } = useAuth()
  const [date, setDate] = useState<Date>(new Date())
  const [viewMode, setViewMode] = useState<"calendar" | "summary">("calendar")

  // Get location name for display
  const getLocationName = (locationId: string) => {
    switch (locationId) {
      case "loc1":
        return "D-ring road"
      case "loc2":
        return "Muaither"
      case "loc3":
        return "Medinat Khalifa"
      case "home":
        return "Home Service"
      case "all":
        return "All Locations"
      default:
        return "Unknown Location"
    }
  }

  // Use the staff provider to get REAL staff data from HR system
  const { staff, getStaffByLocation } = useStaff();

  // Get REAL staff for the current location - NO mock data
  const availableStaff = getStaffByLocation(currentLocation);

  // Debug log to verify we're using REAL staff data from HR system
  console.log("🔍 SalonCalendarView - STAFF DATA AUDIT:");
  console.log("SalonCalendarView - Using REAL staff from HR system");
  console.log("SalonCalendarView - Current Location:", currentLocation);
  console.log("SalonCalendarView - Total Real Staff Count:", staff.length);
  console.log("SalonCalendarView - Available Staff Count for Location:", availableStaff.length);
  console.log("SalonCalendarView - Real Staff Names:", availableStaff.map(s => s.name));
  console.log("SalonCalendarView - Real Staff IDs:", availableStaff.map(s => s.id));
  console.log("SalonCalendarView - Staff with homeService:", staff.filter(s => s.homeService === true).map(s => s.name));
  console.log("SalonCalendarView - Staff data source: useStaff() hook from HR system");
  console.log("SalonCalendarView - NO MOCK DATA USED ✅");

  // Verify we have real staff data (should be exactly 7 real staff members)
  if (staff.length === 0) {
    console.error("❌ SalonCalendarView - CRITICAL: No staff data found! Check HR staff management system.");
  } else if (staff.length !== 7) {
    console.warn(`⚠️ SalonCalendarView - Expected 7 real staff members, found ${staff.length}. Check HR system.`);
  } else {
    console.log("✅ SalonCalendarView - VERIFIED: Using correct number of real staff members (7)");
  }

  // Create time slots for day view
  const timeSlots = []
  for (let i = 0; i < 24; i++) {
    timeSlots.push(`${i}:00 ${i < 12 ? "AM" : "PM"}`)
  }

  // Filter REAL appointments for the current day and location - NO mock data
  const allAppointments = getAllAppointments();
  const todaysAppointments = allAppointments.filter((appointment) => {
    const appointmentDate = parseISO(appointment.date)
    const isSameDay =
      appointmentDate.getDate() === date.getDate() &&
      appointmentDate.getMonth() === date.getMonth() &&
      appointmentDate.getFullYear() === date.getFullYear()

    // Only show appointments at the selected location
    const isCorrectLocation = currentLocation === "all" || appointment.location === currentLocation

    // Get the staff member for this appointment
    const staffMember = staff.find(staffMember => staffMember.id === appointment.staffId);

    // Only show appointments for staff members who are assigned to the selected location
    // This ensures staff members only show appointments at their assigned locations
    const isStaffAssignedToLocation =
      currentLocation === "all" ||
      (staffMember && staffMember.locations.includes(currentLocation)) ||
      (currentLocation === "home" && staffMember && staffMember.homeService);

    return isSameDay && isCorrectLocation && isStaffAssignedToLocation
  })

  // Debug log to help diagnose appointment filtering issues
  console.log("SalonCalendarView - Filtered Appointments:",
    todaysAppointments.map(a => ({
      staffName: a.staffName,
      location: a.location,
      service: a.service
    })));

  // Group appointments by staff and time
  const appointmentsByStaffAndTime: Record<string, Record<string, any[]>> = {}

  availableStaff.forEach((staff) => {
    appointmentsByStaffAndTime[staff.id] = {}
  })

  todaysAppointments.forEach((appointment) => {
    const appointmentDate = parseISO(appointment.date)
    const hour = appointmentDate.getHours()
    const endHour = hour + Math.ceil(appointment.duration / 60)

    if (!appointmentsByStaffAndTime[appointment.staffId]) {
      appointmentsByStaffAndTime[appointment.staffId] = {}
    }

    for (let h = hour; h < endHour; h++) {
      const timeKey = `${h}:00 ${h < 12 ? "AM" : "PM"}`
      if (!appointmentsByStaffAndTime[appointment.staffId][timeKey]) {
        appointmentsByStaffAndTime[appointment.staffId][timeKey] = []
      }
      appointmentsByStaffAndTime[appointment.staffId][timeKey].push(appointment)
    }
  })

  // Navigation functions
  const goToToday = () => setDate(new Date())
  const goToPreviousDay = () => setDate(subDays(date, 1))
  const goToNextDay = () => setDate(addDays(date, 1))

  // Get appointment background color based on service type
  const getAppointmentColor = (service: string) => {
    if (service.toLowerCase().includes("color") || service.toLowerCase().includes("highlights")) {
      return "bg-amber-100 border-l-amber-500"
    } else if (service.toLowerCase().includes("haircut") || service.toLowerCase().includes("style")) {
      return "bg-red-100 border-l-red-500"
    } else if (service.toLowerCase().includes("facial") || service.toLowerCase().includes("mask")) {
      return "bg-blue-100 border-l-blue-500"
    } else if (service.toLowerCase().includes("massage")) {
      return "bg-green-100 border-l-green-500"
    } else {
      return "bg-gray-100 border-l-gray-500"
    }
  }

  // Get staff avatar color
  const getStaffAvatarColor = (index: number) => {
    const colors = ["bg-purple-100", "bg-green-100", "bg-yellow-100", "bg-orange-100", "bg-lime-100", "bg-pink-100"]
    return colors[index % colors.length]
  }

  return (
    <div className="space-y-4">
      <div className="flex flex-col sm:flex-row justify-between gap-4">
        <div className="flex items-center space-x-2">
          <Tabs defaultValue={viewMode} onValueChange={(v) => setViewMode(v as any)} className="mr-4">
            <TabsList>
              <TabsTrigger value="calendar" className="flex items-center gap-1">
                <CalendarIcon className="h-4 w-4" />
                <span>Calendar</span>
              </TabsTrigger>
              <TabsTrigger value="summary" className="flex items-center gap-1">
                <ListIcon className="h-4 w-4" />
                <span>Booking Summary</span>
              </TabsTrigger>
            </TabsList>
          </Tabs>

          <Button
            variant="default"
            size="sm"
            className="rounded-full bg-black text-white hover:bg-gray-800"
            onClick={goToToday}
          >
            Today
          </Button>

          <Button variant="ghost" size="icon" onClick={goToPreviousDay}>
            <ChevronLeft className="h-4 w-4" />
          </Button>

          <div className="text-sm font-medium">{format(date, "EEE dd MMM")}</div>

          <Button variant="ghost" size="icon" onClick={goToNextDay}>
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>

        <div className="flex items-center gap-2">
          <div className="flex">
            <Button
              variant="outline"
              className="rounded-l-md rounded-r-none bg-black text-white hover:bg-gray-800 border-r-0"
            >
              All Locations
            </Button>
            <Button variant="outline" className="rounded-none border-x-0">
              Downtown Salon
            </Button>
            <Button variant="outline" className="rounded-none">
              Westside Salon
            </Button>
            <Button variant="outline" className="rounded-none">
              Northside Salon
            </Button>
            <Button variant="outline" className="rounded-r-md rounded-l-none border-l-0">
              Home Service
            </Button>
          </div>
        </div>
      </div>

      <div className="flex items-center justify-between">
        <Input placeholder="Search appointments..." className="max-w-xs" />

        <div className="flex items-center gap-2">
          <Select defaultValue="day">
            <SelectTrigger className="w-[100px]">
              <SelectValue placeholder="View" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="day">Day</SelectItem>
              <SelectItem value="week">Week</SelectItem>
              <SelectItem value="month">Month</SelectItem>
            </SelectContent>
          </Select>

          <Button onClick={onAddAppointment} className="bg-black text-white hover:bg-gray-800">
            <Plus className="mr-2 h-4 w-4" />
            Add Appointment
          </Button>
        </div>
      </div>

      {viewMode === "calendar" && (
        <div className="border rounded-md overflow-hidden">
          <div className="grid" style={{ gridTemplateColumns: `100px repeat(${availableStaff.length}, 1fr)` }}>
            {/* Header row with staff names */}
            <div className="bg-gray-50 border-b p-2">
              <div className="text-sm font-medium text-gray-600">
                {getLocationName(currentLocation)}
              </div>
            </div>
            {availableStaff.map((staff, index) => (
              <div key={staff.id} className="bg-gray-50 border-b border-l p-2 text-center">
                <div className="mx-auto mb-1">
                  <StaffAvatar
                    staff={staff}
                    size="lg"
                    className="mx-auto"
                  />
                </div>
                <div className="text-sm font-medium">{getFirstName(staff.name)}</div>
              </div>
            ))}

            {/* Time slots */}
            {timeSlots.map((timeSlot, timeIndex) => (
              <React.Fragment key={timeSlot}>
                <div className="border-b p-2 text-xs text-gray-500 text-right pr-4">{timeSlot}</div>

                {availableStaff.map((staff) => {
                  const appointments = appointmentsByStaffAndTime[staff.id]?.[timeSlot] || []
                  const hasAppointment = appointments.length > 0

                  return (
                    <div
                      key={`${staff.id}-${timeSlot}`}
                      className={cn("border-b border-l p-1 min-h-[60px]", hasAppointment ? "relative" : "")}
                    >
                      {hasAppointment &&
                        appointments.map((appointment) => {
                          const appointmentDate = parseISO(appointment.date)
                          const startHour = appointmentDate.getHours()
                          const startTimeSlot = `${startHour}:00 ${startHour < 12 ? "AM" : "PM"}`

                          // Only render the appointment in its starting time slot
                          if (timeSlot === startTimeSlot) {
                            const durationInHours = appointment.duration / 60
                            const heightMultiplier = durationInHours > 1 ? durationInHours : 1

                            return (
                              <div
                                key={appointment.id}
                                className={cn(
                                  "absolute inset-x-1 p-2 rounded-sm border-l-4 cursor-pointer",
                                  getAppointmentColor(appointment.service),
                                )}
                                style={{
                                  height: `${heightMultiplier * 60 - 2}px`,
                                  zIndex: 10,
                                }}
                              >
                                <div className="text-sm font-medium">{appointment.clientName}</div>
                                <div className="text-xs">{appointment.service}</div>
                                <div className="text-xs text-gray-500">
                                  {format(appointmentDate, "h:mm")} -
                                  {format(new Date(appointmentDate.getTime() + appointment.duration * 60000), "h:mm a")}
                                </div>
                              </div>
                            )
                          }
                          return null
                        })}
                    </div>
                  )
                })}
              </React.Fragment>
            ))}
          </div>
        </div>
      )}

      {viewMode === "summary" && (
        <div className="border rounded-md p-4">
          <h3 className="text-lg font-medium mb-4">Booking Summary</h3>
          <div className="space-y-4">
            {todaysAppointments.length > 0 ? (
              todaysAppointments.map((appointment) => {
                const appointmentDate = parseISO(appointment.date)
                return (
                  <div key={appointment.id} className="p-3 border rounded-md flex justify-between items-start">
                    <div>
                      <div className="font-medium">{appointment.clientName}</div>
                      <div className="text-sm text-gray-500">
                        {format(appointmentDate, "h:mm a")} -
                        {format(new Date(appointmentDate.getTime() + appointment.duration * 60000), "h:mm a")}
                      </div>
                    </div>
                    <div className="text-right">
                      <div>{appointment.service}</div>
                      <div className="text-sm text-gray-500">
                        with {staff.find((s) => s.id === appointment.staffId)?.name}
                      </div>
                    </div>
                  </div>
                )
              })
            ) : (
              <div className="text-center py-8 text-gray-500">No appointments scheduled for today.</div>
            )}
          </div>
        </div>
      )}
    </div>
  )
}

